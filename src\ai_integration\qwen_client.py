"""
Qwen AI integration for MignalyBot
"""

import logging
import json
import re
import asyncio
from datetime import datetime, timezone
import httpx
from sqlalchemy import select

from src.database.setup import get_async_db
from src.database.models import Config, PromptTemplate

logger = logging.getLogger(__name__)

# Qwen API endpoint
QWEN_API_ENDPOINT = "https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions"

class QwenClient:
    """Client for interacting with Qwen AI API"""

    def __init__(self, api_key=None):
        """
        Initialize the Qwen client

        Args:
            api_key (str, optional): Qwen API key. If not provided, will be loaded from database.
        """
        self.api_key = api_key

    def _process_template_conditionals(self, template, context):
        """
        Process conditional logic in templates like {if take_profit_2}...{endif}

        Args:
            template (str): Template string with conditional logic
            context (dict): Context variables for evaluation

        Returns:
            str: Processed template with conditionals resolved
        """
        # Pattern to match {if variable}...{endif} blocks
        pattern = r'\{if\s+([^}]+)\}(.*?)\{endif\}'

        def replace_conditional(match):
            condition = match.group(1).strip()
            content = match.group(2)

            # Evaluate the condition - check if variable exists and has a truthy value
            if condition in context:
                value = context[condition]
                # Consider empty strings, None, False, 0 as falsy
                if value and str(value).strip():
                    return content

            return ""

        # Process all conditional blocks
        processed = re.sub(pattern, replace_conditional, template, flags=re.DOTALL)
        return processed

    async def ensure_api_key(self):
        """Ensure API key is set, loading from database if needed"""
        if not self.api_key:
            from src.database.setup import is_sqlite_db
            async for db in get_async_db():
                # Handle SQLite differently than other databases
                if is_sqlite_db():
                    config_result = db.execute(select(Config))
                else:
                    config_result = await db.execute(select(Config))

                config = config_result.scalars().first()

                if config and config.qwen_api_key:
                    self.api_key = config.qwen_api_key
                else:
                    raise ValueError("Qwen API key not found in configuration")

    async def get_prompt_template(self, post_type, language="fa"):
        """
        Get prompt template from database for specific post type and language

        Args:
            post_type (str): Type of post (news, events, signals, etc.)
            language (str): Language code (fa, en, ar, etc.)

        Returns:
            str: Prompt template content or None if not found
        """
        try:
            from src.database.setup import is_sqlite_db
            async for db in get_async_db():
                if is_sqlite_db():
                    result = db.execute(
                        select(PromptTemplate).where(
                            PromptTemplate.post_type == post_type,
                            PromptTemplate.language == language,
                            PromptTemplate.active == True
                        )
                    )
                else:
                    result = await db.execute(
                        select(PromptTemplate).where(
                            PromptTemplate.post_type == post_type,
                            PromptTemplate.language == language,
                            PromptTemplate.active == True
                        )
                    )

                template = result.scalars().first()
                if template:
                    logger.info(f"📝 Loaded prompt template for {post_type} ({language}) from database")
                    return template.template_content
                else:
                    logger.warning(f"⚠️ No prompt template found for {post_type} ({language}) in database")
                    return None
        except Exception as e:
            logger.error(f"❌ Error loading prompt template for {post_type} ({language}): {e}")
            return None

    async def generate_content(self, prompt, max_tokens=1000, temperature=0.7, language="en"):
        """
        Generate content using Qwen AI

        Args:
            prompt (str): Prompt for content generation
            max_tokens (int, optional): Maximum tokens to generate
            temperature (float, optional): Temperature for generation
            language (str, optional): Language for the response

        Returns:
            str: Generated content
        """
        await self.ensure_api_key()

        if not self.api_key:
            logger.error("No Qwen API key available")
            return None  # Return None instead of error message

        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Create a language-specific system message
            system_message = "You are a financial analyst and content creator for a trading channel. You provide insightful, accurate, and engaging analysis of market data, news, and economic events. Your style is conversational and accessible, avoiding overly formal language. You use clear explanations that both beginners and experienced traders can understand."

            # Add explicit language instruction to system message
            if language == "en":
                system_message += " IMPORTANT: You must respond in English only."
            elif language == "ar":
                system_message += " IMPORTANT: You must respond in Arabic (العربية) only."
            elif language == "fa":
                system_message += " IMPORTANT: You must respond in Farsi (فارسی) only."
            else:
                system_message += f" IMPORTANT: You must respond in {language} language only."

            payload = {
                "model": "qwen-max-2025-01-25",
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": max_tokens,
                "temperature": temperature
            }

            logger.info(f"🌐 Making Qwen API call...")
            logger.info(f"📡 Endpoint: {QWEN_API_ENDPOINT}")
            logger.info(f"🤖 Model: {payload['model']}")
            logger.info(f"🌡️ Temperature: {payload['temperature']}")
            logger.info(f"📏 Max tokens: {payload['max_tokens']}")
            logger.info(f"🗣️ Language: {language}")
            logger.info(f"📝 System message: {system_message[:200]}..." if len(system_message) > 200 else f"📝 System message: {system_message}")
            logger.info(f"📋 User prompt length: {len(prompt)} characters")
            logger.info(f"📋 User prompt preview: {prompt[:300]}..." if len(prompt) > 300 else f"📋 User prompt: {prompt}")

            # Enhanced HTTP client with retry logic for network errors
            max_retries = 3
            retry_delay = 2

            for attempt in range(max_retries):
                try:
                    async with httpx.AsyncClient(
                        timeout=httpx.Timeout(60.0, connect=10.0),  # 60s total, 10s connect timeout
                        limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
                    ) as client:
                        response = await client.post(
                            QWEN_API_ENDPOINT,
                            headers=headers,
                            json=payload
                        )

                        logger.info(f"📡 API Response Status: {response.status_code} (attempt {attempt + 1})")

                        if response.status_code != 200:
                            logger.error(f"💥 Qwen API error: {response.status_code} {response.text}")
                            return None  # Return None instead of error message



                        # Parse JSON response
                        try:
                            if not response.text.strip():
                                logger.error("💥 Empty response from Qwen API")
                                return None

                            result = response.json()

                        except ValueError as e:
                            logger.error(f"💥 Invalid JSON response from Qwen API: {e}")
                            logger.error(f"Response content: {response.text[:500]}")
                            return None
                        except Exception as e:
                            logger.error(f"💥 UNEXPECTED ERROR during JSON parsing: {e}")
                            logger.error(f"Response content: {response.text[:500]}")
                            return None

                        logger.info(f"📦 API Response received, processing...")

                        # Log usage information if available
                        if "usage" in result:
                            usage = result["usage"]
                            logger.info(f"📊 Token usage: prompt={usage.get('prompt_tokens', 'N/A')}, completion={usage.get('completion_tokens', 'N/A')}, total={usage.get('total_tokens', 'N/A')}")

                        if "choices" in result and result["choices"] and len(result["choices"]) > 0:
                            if "message" in result["choices"][0] and "content" in result["choices"][0]["message"]:
                                content = result["choices"][0]["message"]["content"]
                                logger.info(f"✅ Successfully received content from Qwen API")
                                logger.info(f"📝 Response length: {len(content)} characters")
                                logger.info(f"📋 Response preview: {content[:200]}..." if len(content) > 200 else f"📋 Response: {content}")
                                return content
                            else:
                                logger.error(f"💥 Unexpected API response structure in choices[0]: {result['choices'][0] if result['choices'] else 'No choices'}")
                                return None  # Return None instead of error message
                        else:
                            logger.error(f"💥 Missing or empty choices in API response")
                            logger.error(f"💥 Full response structure: {result}")
                            return None  # Return None instead of error message

                except (httpx.ConnectError, httpx.TimeoutException, httpx.NetworkError) as e:
                    logger.warning(f"🌐 Network error on attempt {attempt + 1}/{max_retries}: {type(e).__name__}: {e}")

                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                        logger.info(f"⏳ Retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"❌ All {max_retries} attempts failed for Qwen API")
                        return None

                except httpx.HTTPStatusError as e:
                    logger.error(f"💥 HTTP status error: {e.response.status_code}")
                    return None

                except Exception as e:
                    logger.error(f"💥 Unexpected error in Qwen API call: {e}", exc_info=True)
                    return None

            # If we reach here, all retry attempts failed
            logger.error(f"❌ All {max_retries} attempts failed for Qwen API")
            return None

        except Exception as e:
            logger.error(f"Error generating content with Qwen: {e}", exc_info=True)
            return None  # Return None instead of error message

    async def analyze_news(self, news_item, language="en", channel_brand=None):
        """
        Analyze a news item using database prompt template

        Args:
            news_item (NewsItem): News item to analyze
            language (str, optional): Language for analysis
            channel_brand (str, optional): Channel brand name

        Returns:
            str: Analysis of the news item
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("news", language)

        # Process channel brand for handle
        if channel_brand:
            # Clean up channel brand for handle - remove spaces, convert to lowercase
            # Handle common patterns like 'bot' -> 'fx', remove special characters
            import re
            channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
            # Remove any non-alphanumeric characters except underscores
            channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
        else:
            channel_handle = 'tradingchannel'

        if template_content:
            # Use database template and format it with actual values
            prompt = template_content.format(
                title=news_item.title,
                content=news_item.content,
                source=news_item.source,
                language=language,
                channel_brand=channel_brand or 'Trading Channel',
                channel_handle=channel_handle
            )
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for news analysis ({language})")
            raise ValueError(f"No prompt template found for news analysis in language: {language}")

        return await self.generate_content(prompt, max_tokens=500, language=language)

    def _get_country_flag(self, text):
        """Get country flag emoji based on text content"""
        text_lower = text.lower()

        # Country mappings
        country_flags = {
            'usa': '🇺🇸', 'united states': '🇺🇸', 'america': '🇺🇸', 'usd': '🇺🇸',
            'germany': '🇩🇪', 'german': '🇩🇪', 'eur': '🇪🇺', 'euro': '🇪🇺',
            'japan': '🇯🇵', 'japanese': '🇯🇵', 'jpy': '🇯🇵', 'yen': '🇯🇵',
            'uk': '🇬🇧', 'britain': '🇬🇧', 'gbp': '🇬🇧', 'pound': '🇬🇧',
            'canada': '🇨🇦', 'canadian': '🇨🇦', 'cad': '🇨🇦',
            'australia': '🇦🇺', 'australian': '🇦🇺', 'aud': '🇦🇺',
            'switzerland': '🇨🇭', 'swiss': '🇨🇭', 'chf': '🇨🇭',
            'new zealand': '🇳🇿', 'nzd': '🇳🇿',
            'china': '🇨🇳', 'chinese': '🇨🇳', 'cny': '🇨🇳'
        }

        for country, flag in country_flags.items():
            if country in text_lower:
                return flag

        return '🌍'  # Default global flag

    async def analyze_economic_event(self, event, language="en", channel_brand=None):
        """
        Analyze an economic calendar event using database prompt template

        Args:
            event (EconomicEvent): Economic event to analyze
            language (str, optional): Language for analysis
            channel_brand (str, optional): Channel brand name

        Returns:
            str: Analysis of the economic event
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("events", language)

        impact_level = "Low" if event.impact == 1 else "Medium" if event.impact == 2 else "High" if event.impact == 3 else "Unknown"

        # Get country flag
        country_flag = self._get_country_flag(event.country or event.title)

        # Get relevant emoji based on event type
        event_lower = event.title.lower()
        relevant_emoji = "📊"  # Default

        if any(word in event_lower for word in ["employment", "jobs", "payroll", "unemployment", "اشتغال"]):
            relevant_emoji = "👥"
        elif any(word in event_lower for word in ["inflation", "cpi", "ppi", "تورم"]):
            relevant_emoji = "📈"
        elif any(word in event_lower for word in ["gdp", "growth", "رشد"]):
            relevant_emoji = "🏭"
        elif any(word in event_lower for word in ["rate", "interest", "fed", "ecb", "نرخ"]):
            relevant_emoji = "🏦"
        elif any(word in event_lower for word in ["trade", "balance", "export", "import", "تجارت"]):
            relevant_emoji = "🌍"
        elif any(word in event_lower for word in ["retail", "sales", "consumer", "فروش"]):
            relevant_emoji = "🛒"

        # Process channel brand for handle
        if channel_brand:
            # Clean up channel brand for handle - remove spaces, convert to lowercase
            # Handle common patterns like 'bot' -> 'fx', remove special characters
            channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
            # Remove any non-alphanumeric characters except underscores
            import re
            channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
        else:
            channel_handle = 'tradingchannel'

        if template_content:
            # Use database template and format it with actual values
            prompt = template_content.format(
                title=event.title,
                country=event.country or 'N/A',
                currency=event.currency or 'N/A',
                previous=event.previous or 'N/A',
                forecast=event.forecast or 'N/A',
                actual=event.actual or 'N/A',
                impact_level=impact_level,
                language=language,
                channel_brand=channel_brand or 'Trading Channel',
                country_flag=country_flag,
                relevant_emoji=relevant_emoji,
                channel_handle=channel_handle
            )
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for events analysis ({language})")
            raise ValueError(f"No prompt template found for events analysis in language: {language}")

        return await self.generate_content(prompt, max_tokens=500, language=language)

    async def analyze_trading_signal(self, signal, language="en", channel_brand=None):
        """
        Analyze a trading signal using database prompt template

        Args:
            signal (TradingSignal): Trading signal to analyze
            language (str, optional): Language for analysis
            channel_brand (str, optional): Channel brand name

        Returns:
            str: Analysis of the trading signal
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("signals", language)

        from src.utils.helpers import format_trading_price

        direction = "LONG" if signal.direction == "buy" else "SHORT"

        # Format prices to maximum 4 digits
        entry_price = format_trading_price(signal.entry_price)
        stop_loss = format_trading_price(signal.stop_loss)
        take_profit = format_trading_price(signal.take_profit)

        # Process channel brand for handle
        if channel_brand:
            # Clean up channel brand for handle - remove spaces, convert to lowercase
            # Handle common patterns like 'bot' -> 'fx', remove special characters
            channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
            # Remove any non-alphanumeric characters except underscores
            import re
            channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
        else:
            channel_handle = 'tradingchannel'

        if template_content:
            # Handle multi-TP levels
            take_profit_1 = format_trading_price(signal.take_profit_1) if hasattr(signal, 'take_profit_1') and signal.take_profit_1 else take_profit
            take_profit_2 = format_trading_price(signal.take_profit_2) if hasattr(signal, 'take_profit_2') and signal.take_profit_2 else ""
            take_profit_3 = format_trading_price(signal.take_profit_3) if hasattr(signal, 'take_profit_3') and signal.take_profit_3 else ""

            # Calculate position percentages based on number of TPs
            tp_count = sum([1 for tp in [take_profit_1, take_profit_2, take_profit_3] if tp])

            if tp_count == 1:
                tp1_percentage, tp2_percentage, tp3_percentage = 100, 0, 0
            elif tp_count == 2:
                tp1_percentage, tp2_percentage, tp3_percentage = 50, 50, 0
            else:  # tp_count == 3
                tp1_percentage, tp2_percentage, tp3_percentage = 33, 33, 34

            # Create conditional lines for TP2 and TP3
            take_profit_2_line = f"\n🎯 هدف دوم (TP2): {take_profit_2} - {tp2_percentage}% پوزیشن" if take_profit_2 else ""
            take_profit_3_line = f"\n🎯 هدف سوم (TP3): {take_profit_3} - {tp3_percentage}% پوزیشن" if take_profit_3 else ""

            # Process template to handle conditional logic
            processed_template = self._process_template_conditionals(template_content, {
                'take_profit_2': take_profit_2,
                'take_profit_3': take_profit_3,
                'has_tp2': bool(take_profit_2),
                'has_tp3': bool(take_profit_3)
            })

            # Use processed template and format it with actual values
            prompt = processed_template.format(
                symbol=signal.symbol,
                timeframe=signal.timeframe,
                direction=direction,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,  # Legacy field
                take_profit_1=take_profit_1,
                take_profit_2=take_profit_2,
                take_profit_3=take_profit_3,
                tp1_percentage=tp1_percentage,
                tp2_percentage=tp2_percentage,
                tp3_percentage=tp3_percentage,
                take_profit_2_line=take_profit_2_line,
                take_profit_3_line=take_profit_3_line,
                has_tp2=bool(take_profit_2),
                has_tp3=bool(take_profit_3),
                notes=signal.notes or "",
                language=language,
                channel_brand=channel_brand or 'Trading Channel',
                channel_handle=channel_handle
            )
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for signals analysis ({language})")
            raise ValueError(f"No prompt template found for signals analysis in language: {language}")

        return await self.generate_content(prompt, max_tokens=1000, language=language)

    async def generate_signal_update(self, signal, language="en", channel_brand=None, tp_level=None, include_break_even=False):
        """
        Generate a concise update for a trading signal (trade result) using database prompt template

        Args:
            signal (TradingSignal): Trading signal to update
            language (str, optional): Language for analysis
            channel_brand (str, optional): Channel brand name
            tp_level (int, optional): Which TP level was hit (1, 2, 3) for partial updates
            include_break_even (bool, optional): Whether to include break-even message (for TP1)

        Returns:
            str: Update for the trading signal
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("signal_update", language)

        from src.utils.helpers import format_trading_price

        direction = "LONG" if signal.direction == "buy" else "SHORT"
        status = signal.status.value.upper()
        # Safely format profit_loss, handling None values
        if signal.profit_loss is not None:
            try:
                profit_loss = f"{float(signal.profit_loss):.1f}%"
            except (ValueError, TypeError):
                profit_loss = "0%"
        else:
            profit_loss = "0%"

        # Format prices
        entry_price = format_trading_price(signal.entry_price)
        exit_price = format_trading_price(signal.exit_price) if signal.exit_price else "N/A"

        # Determine result emoji and TP level text
        result_emoji = "✅" if signal.profit_loss and signal.profit_loss > 0 else "❌" if signal.profit_loss and signal.profit_loss < 0 else "⏸️"

        # Handle multi-TP level information
        tp_level_text = ""
        break_even_message = ""

        if tp_level:
            tp_level_text = f"TP{tp_level}"
            if tp_level == 1 and include_break_even:
                # Multilingual break-even messages
                if language == "fa":
                    break_even_message = "🔒 استاپ لاس به نقطه سر به سر منتقل شد"
                elif language == "ar":
                    break_even_message = "🔒 تم نقل وقف الخسارة إلى نقطة التعادل"
                else:  # English
                    break_even_message = "🔒 Stop Loss moved to Break Even"
        else:
            # Final result or legacy signal
            tp_level_text = "FINAL"

        # Process channel brand for handle
        if channel_brand:
            # Clean up channel brand for handle - remove spaces, convert to lowercase
            # Handle common patterns like 'bot' -> 'fx', remove special characters
            channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
            # Remove any non-alphanumeric characters except underscores
            import re
            channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
        else:
            channel_handle = 'tradingchannel'

        if template_content:
            # Handle multi-TP levels for update templates
            take_profit_1 = format_trading_price(signal.take_profit_1) if hasattr(signal, 'take_profit_1') and signal.take_profit_1 else format_trading_price(signal.take_profit)
            take_profit_2 = format_trading_price(signal.take_profit_2) if hasattr(signal, 'take_profit_2') and signal.take_profit_2 else ""
            take_profit_3 = format_trading_price(signal.take_profit_3) if hasattr(signal, 'take_profit_3') and signal.take_profit_3 else ""

            # Calculate position percentages based on number of TPs
            tp_count = sum([1 for tp in [take_profit_1, take_profit_2, take_profit_3] if tp])

            if tp_count == 1:
                tp1_percentage, tp2_percentage, tp3_percentage = 100, 0, 0
            elif tp_count == 2:
                tp1_percentage, tp2_percentage, tp3_percentage = 50, 50, 0
            else:  # tp_count == 3
                tp1_percentage, tp2_percentage, tp3_percentage = 33, 33, 34

            # Create conditional lines for TP2 and TP3
            take_profit_2_line = f"\n🎯 هدف دوم (TP2): {take_profit_2} - {tp2_percentage}% پوزیشن" if take_profit_2 else ""
            take_profit_3_line = f"\n🎯 هدف سوم (TP3): {take_profit_3} - {tp3_percentage}% پوزیشن" if take_profit_3 else ""

            # Process template to handle conditional logic
            processed_template = self._process_template_conditionals(template_content, {
                'take_profit_2': take_profit_2,
                'take_profit_3': take_profit_3,
                'has_tp2': bool(take_profit_2),
                'has_tp3': bool(take_profit_3)
            })

            # Use processed template and format it with actual values
            prompt = processed_template.format(
                symbol=signal.symbol,
                direction=direction,
                entry_price=entry_price,
                exit_price=exit_price,
                profit_loss=profit_loss,
                status=status,
                tp_level=tp_level or 0,
                tp_level_text=tp_level_text,
                break_even_message=break_even_message,
                take_profit_1=take_profit_1,
                take_profit_2=take_profit_2,
                take_profit_3=take_profit_3,
                tp1_percentage=tp1_percentage,
                tp2_percentage=tp2_percentage,
                tp3_percentage=tp3_percentage,
                take_profit_2_line=take_profit_2_line,
                take_profit_3_line=take_profit_3_line,
                has_tp2=bool(take_profit_2),
                has_tp3=bool(take_profit_3),
                channel_brand=channel_brand or 'Trading Channel',
                result_emoji=result_emoji,
                channel_handle=channel_handle,
                language=language
            )
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for signal_update analysis ({language})")
            raise ValueError(f"No prompt template found for signal_update analysis in language: {language}")

        return await self.generate_content(prompt, max_tokens=500, language=language)

    async def generate_market_analysis(self, symbol, timeframe, candles, language="en", channel_brand=None):
        """
        Generate market analysis for a symbol using database prompt template

        Args:
            symbol (str): Symbol to analyze
            timeframe (str): Timeframe to analyze
            candles (list): List of candle data
            language (str, optional): Language for analysis
            channel_brand (str, optional): Channel brand name

        Returns:
            str: Market analysis
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("market_analysis", language)

        from src.utils.helpers import format_trading_price

        # Get latest candle data
        latest_candle = candles[0] if candles else None
        if latest_candle:
            current_price = format_trading_price(latest_candle.close)
            # Safely calculate change, handling None values
            try:
                if latest_candle.open and latest_candle.close:
                    change = ((latest_candle.close - latest_candle.open) / latest_candle.open * 100)
                else:
                    change = 0
            except (TypeError, ZeroDivisionError):
                change = 0
        else:
            current_price = "N/A"
            change = 0

        # Safely format change
        try:
            change_str = f"{float(change):+.1f}%"
        except (ValueError, TypeError):
            change_str = "+0.0%"

        if template_content:
            # Use database template and format it with actual values
            prompt = template_content.format(
                symbol=symbol,
                timeframe=timeframe,
                current_price=current_price,
                change_str=change_str,
                language=language,
                channel_handler="@mignalyfx"  # Default channel handler
            )
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for market_analysis ({language})")
            raise ValueError(f"No prompt template found for market_analysis in language: {language}")

        return await self.generate_content(prompt, max_tokens=300, language=language)

    async def generate_countdown_post(self, event, language="en", channel_brand=None):
        """
        Generate a concise countdown post for an upcoming economic event using database prompt template

        Args:
            event (EconomicEvent): Economic event to create countdown for
            language (str, optional): Language for post
            channel_brand (str, optional): Channel brand name

        Returns:
            str: Countdown post
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("countdown", language)

        from datetime import datetime, timezone

        # Get country flag
        country_flag = self._get_country_flag(event.country or event.title)

        # Ensure event_time is timezone-aware for calculation
        if event.event_time.tzinfo is None:
            event_time_utc = event.event_time.replace(tzinfo=timezone.utc)
        else:
            event_time_utc = event.event_time.astimezone(timezone.utc)

        # For scheduled posts, we need dynamic countdown calculation
        # Use a placeholder that will be replaced when the message is actually sent
        time_until = event_time_utc - datetime.now(timezone.utc)
        minutes_until = int(time_until.total_seconds() / 60)

        # Store event time for dynamic calculation later
        event_time_iso = event_time_utc.isoformat()

        # Impact emoji
        impact_emoji = "🔴" if event.impact == 3 else "🟡" if event.impact == 2 else "🟢"

        # Process channel brand for handle
        if channel_brand:
            # Clean up channel brand for handle - remove spaces, convert to lowercase
            # Handle common patterns like 'bot' -> 'fx', remove special characters
            channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
            # Remove any non-alphanumeric characters except underscores
            import re
            channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
        else:
            channel_handle = 'tradingchannel'

        if template_content:
            # Use database template and format it with actual values
            # For scheduled posts, use dynamic countdown placeholder
            prompt = template_content.format(
                title=event.title,
                country=event.country or 'N/A',
                currency=event.currency or 'N/A',
                previous=event.previous or 'N/A',
                forecast=event.forecast or 'N/A',
                minutes_until="{{DYNAMIC_COUNTDOWN}}",  # Placeholder for dynamic calculation
                language=language,
                channel_brand=channel_brand or 'Trading Channel',
                country_flag=country_flag,
                impact_emoji=impact_emoji,
                channel_handle=channel_handle,
                event_time_iso=event_time_iso  # Store event time for later calculation
            )

            # Add hidden event time marker for dynamic calculation
            # This will be removed by the formatter after calculation
            prompt += f"\n<!-- EVENT_TIME: {event_time_iso} -->"
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for countdown ({language})")
            raise ValueError(f"No prompt template found for countdown in language: {language}")

        return await self.generate_content(prompt, max_tokens=300, language=language)

    async def generate_daily_greeting(self, events, language="en", channel_brand=None):
        """
        Generate a daily greeting message with today's events using database prompt template

        Args:
            events (list): List of economic events for today
            language (str, optional): Language for the greeting
            channel_brand (str, optional): Channel brand name

        Returns:
            str: Daily greeting message
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("greeting", language)

        from datetime import datetime, timezone
        import pytz

        # Get Tehran time for greeting
        tehran_tz = pytz.timezone('Asia/Tehran')
        tehran_time = datetime.now(tehran_tz)
        today_date = tehran_time.strftime('%Y-%m-%d')
        current_hour = tehran_time.hour

        # Determine greeting based on time
        if current_hour < 12:
            time_greeting = "Good Morning" if language == "en" else "صبح بخیر"
        elif current_hour < 17:
            time_greeting = "Good Afternoon" if language == "en" else "ظهر بخیر"
        else:
            time_greeting = "Good Evening" if language == "en" else "عصر بخیر"

        # Format ALL events for display (not just limited to 3)
        high_impact_events = []
        medium_impact_events = []
        low_impact_events = []

        if events:
            for event in events:
                impact_emoji = "🔴" if event.impact == 3 else "🟡" if event.impact == 2 else "🟢"
                event_time = event.event_time.strftime('%H:%M')
                country_flag = self._get_country_flag(event.country or event.title)

                event_line = f"{impact_emoji} {event_time} {country_flag} {event.title} ({event.currency or 'N/A'})"

                if event.impact == 3:
                    high_impact_events.append(event_line)
                elif event.impact == 2:
                    medium_impact_events.append(event_line)
                else:
                    low_impact_events.append(event_line)

        # Create comprehensive events summary with ALL events
        events_summary = ""
        total_events = len(events) if events else 0

        if high_impact_events:
            events_summary += f"\n🔴 High Impact Events:\n" + "\n".join(high_impact_events)

        if medium_impact_events:
            events_summary += f"\n🟡 Medium Impact Events:\n" + "\n".join(medium_impact_events)

        if low_impact_events:
            events_summary += f"\n🟢 Low Impact Events:\n" + "\n".join(low_impact_events)

        if not events_summary:
            events_summary = "\n📅 No economic events scheduled today"

        # Format date for display
        if language == "fa":
            # Persian date format
            day_name = tehran_time.strftime('%A')
            day_names_fa = {
                'Monday': 'دوشنبه', 'Tuesday': 'سه‌شنبه', 'Wednesday': 'چهارشنبه',
                'Thursday': 'پنج‌شنبه', 'Friday': 'جمعه', 'Saturday': 'شنبه', 'Sunday': 'یکشنبه'
            }
            day_name_fa = day_names_fa.get(day_name, day_name)

            # Convert numbers to Persian digits
            persian_digits = {'0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴', '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'}
            day_num = str(tehran_time.day)
            month_num = str(tehran_time.month)
            year_num = str(tehran_time.year)

            for en, fa in persian_digits.items():
                day_num = day_num.replace(en, fa)
                month_num = month_num.replace(en, fa)
                year_num = year_num.replace(en, fa)

            formatted_date = f"{day_name_fa} {day_num} {month_num} {year_num}"
        else:
            formatted_date = tehran_time.strftime('%A %d %B %Y')

        # Get main currency from events
        main_currency = "USD"  # Default
        if events:
            currency_counts = {}
            for event in events:
                currency = event.currency or event.country or "USD"
                currency_counts[currency] = currency_counts.get(currency, 0) + 1
            main_currency = max(currency_counts, key=currency_counts.get)

        if template_content:
            # Use database template and format it with actual values
            prompt = template_content.format(
                today_date=today_date,
                time_greeting=time_greeting,
                total_events=total_events,
                channel_brand=channel_brand or ('کانال تریدینگ' if language == 'fa' else 'Trading Channel'),
                main_currency=main_currency,
                events_summary=events_summary,
                formatted_date=formatted_date,
                language=language
            )
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for greeting ({language})")
            raise ValueError(f"No prompt template found for greeting in language: {language}")

        return await self.generate_content(prompt, max_tokens=1500, language=language)

    async def analyze_event_result(self, db_event, latest_event, language="en", channel_brand=None):
        """
        Analyze an economic event result with actual value using database prompt template

        Args:
            db_event (EconomicEvent): Database event record
            latest_event (dict): Latest event data with actual value
            language (str, optional): Language for analysis
            channel_brand (str, optional): Channel brand name

        Returns:
            str: Analysis of the event result
        """
        # Try to get prompt template from database
        template_content = await self.get_prompt_template("event_result", language)

        impact_level = "Low" if db_event.impact == 1 else "Medium" if db_event.impact == 2 else "High"
        impact_emoji = "🔴" if db_event.impact == 3 else "🟡" if db_event.impact == 2 else "🟢"

        # Get country flag
        country_flag = self._get_country_flag(db_event.country or db_event.title)

        # Process channel brand for handle
        if channel_brand:
            channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
            import re
            channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
        else:
            channel_handle = 'tradingchannel'

        if template_content:
            # Use database template and format it with actual values
            prompt = template_content.format(
                title=db_event.title,
                country=db_event.country or 'N/A',
                currency=db_event.currency or 'N/A',
                previous=db_event.previous or 'N/A',
                forecast=db_event.forecast or 'N/A',
                actual=latest_event.get('actual', 'N/A'),
                impact_level=impact_level,
                language=language,
                country_flag=country_flag,
                impact_emoji=impact_emoji,
                channel_handle=channel_handle
            )
        else:
            # No template found - this should not happen with unified prompts
            logger.error(f"❌ No prompt template found for event_result analysis ({language})")
            raise ValueError(f"No prompt template found for event_result analysis in language: {language}")

        return await self.generate_content(prompt, max_tokens=500, language=language)
