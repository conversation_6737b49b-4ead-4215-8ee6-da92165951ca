"""
Telegram bot implementation for MignalyBot
"""

import os
import logging
import asyncio
import re
import httpx
import random
from datetime import datetime, timedelta, timezone
from telegram import Update, Bot
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
)
from telegram.error import TelegramError, TimedOut, RetryAfter, NetworkError
from sqlalchemy import select, func, or_
from sqlalchemy.orm import selectinload

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Post, Channel, PostStatus, PostType
from src.utils.message_formatter import format_message_for_channel

logger = logging.getLogger(__name__)

def validate_image_file(file_path):
    """
    Validate that an image file exists and is not empty

    Args:
        file_path (str): Path to the image file

    Returns:
        bool: True if file is valid, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"Image file does not exist: {file_path}")
            return False

        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.error(f"Image file is empty (0 bytes): {file_path}")
            return False

        if file_size < 100:  # Very small files are likely corrupted
            logger.warning(f"Image file is very small ({file_size} bytes): {file_path}")
            return False

        logger.debug(f"Image file validation passed: {file_path} ({file_size} bytes)")
        return True

    except Exception as e:
        logger.error(f"Error validating image file {file_path}: {e}")
        return False

# Fallback image URLs for when original images are not accessible
FALLBACK_IMAGES = [
    "https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=600&fit=crop&crop=center",  # Financial chart
    "https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=600&fit=crop&crop=center",  # Trading screen
    "https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=800&h=600&fit=crop&crop=center",  # Market data
    "https://images.unsplash.com/photo-1642790106117-e829e14a795f?w=800&h=600&fit=crop&crop=center",  # Financial news
]

async def get_working_image_url(original_url):
    """
    Test if an image URL is accessible and return a working URL.
    Falls back to alternative images if the original fails.

    Note: This function is only for HTTP/HTTPS URLs. Local file paths should be handled separately.

    Args:
        original_url (str): The original image URL to test

    Returns:
        str: A working image URL, or None if no working URL is found
    """
    if not original_url:
        return None

    # Check if this is a local file path (should not be processed by this function)
    if not original_url.startswith(('http://', 'https://')):
        logger.warning(f"Local file path passed to get_working_image_url (should use image_path instead): {original_url}")
        return None

    # Test the original URL first
    if await test_image_url_accessibility(original_url):
        logger.info(f"Original image URL is accessible: {original_url}")
        return original_url

    logger.warning(f"Original image URL not accessible: {original_url}")

    # Try fallback images
    for fallback_url in FALLBACK_IMAGES:
        if await test_image_url_accessibility(fallback_url):
            logger.info(f"Using fallback image: {fallback_url}")
            return fallback_url

    logger.error("No working image URLs found, including fallbacks")
    return None

async def test_image_url_accessibility(url):
    """
    Test if an image URL is accessible by making a HEAD request.

    Args:
        url (str): The image URL to test

    Returns:
        bool: True if the URL is accessible, False otherwise
    """
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:  # Increased timeout
            response = await client.head(url, follow_redirects=True)
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if content_type.startswith('image/'):
                    return True
                else:
                    logger.debug(f"URL is not an image (content-type: {content_type}): {url}")
                    return False
            else:
                logger.debug(f"URL not accessible (status: {response.status_code}): {url}")
                return False
    except (httpx.ConnectError, httpx.TimeoutException) as e:
        logger.debug(f"Network error testing URL accessibility: {type(e).__name__}: {e} - {url}")
        return False
    except Exception as e:
        logger.debug(f"Error testing URL accessibility: {type(e).__name__}: {e} - {url}")
        return False

# Get bot token directly from .env file to avoid issues with colon character
def get_telegram_bot_token():
    """Get Telegram bot token directly from .env file"""
    try:
        with open(".env", "r") as f:
            for line in f:
                line = line.strip()
                if line.startswith("TELEGRAM_BOT_TOKEN="):
                    return line.split("=", 1)[1]
    except Exception as e:
        logger.error(f"Error reading .env file: {e}")

    # Fallback to environment variable
    return os.getenv("TELEGRAM_BOT_TOKEN")

TELEGRAM_BOT_TOKEN = get_telegram_bot_token()

async def send_sticker_with_retry(bot: Bot, chat_id: str, sticker, max_retries: int = 3, timeout: int = 30):
    """
    Send a sticker with retry logic for handling timeouts and network errors

    Args:
        bot (Bot): Telegram bot instance
        chat_id (str): Chat ID to send the sticker to
        sticker: Sticker to send (file path or file object)
        max_retries (int): Maximum number of retry attempts
        timeout (int): Timeout in seconds for each attempt

    Returns:
        Message: Sent message object

    Raises:
        TelegramError: If all retry attempts fail
    """
    last_error = None

    for attempt in range(max_retries + 1):
        try:
            logger.info(f"🎯 Sending sticker (attempt {attempt + 1}/{max_retries + 1})")

            # Prepare send parameters with timeouts
            send_params = {
                "chat_id": chat_id,
                "sticker": sticker,
                "read_timeout": timeout,
                "write_timeout": timeout,
                "connect_timeout": timeout,
                "pool_timeout": timeout
            }

            # Send the sticker
            message = await bot.send_sticker(**send_params)
            logger.info(f"✅ Sticker sent successfully on attempt {attempt + 1}")
            return message

        except TimedOut as e:
            last_error = e
            logger.warning(f"⏰ Sticker timeout on attempt {attempt + 1}: {e}")
            if attempt < max_retries:
                wait_time = (attempt + 1) * 2  # Exponential backoff
                logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)

        except RetryAfter as e:
            last_error = e
            logger.warning(f"🚫 Rate limited, waiting {e.retry_after} seconds")
            await asyncio.sleep(e.retry_after)
            # Don't count rate limit as a retry attempt
            continue

        except NetworkError as e:
            last_error = e
            logger.warning(f"🌐 Network error on attempt {attempt + 1}: {e}")
            if attempt < max_retries:
                wait_time = (attempt + 1) * 3  # Longer wait for network issues
                logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)

        except TelegramError as e:
            # For telegram errors, don't retry
            logger.error(f"❌ Telegram error (not retrying): {e}")
            raise e

    # All retries failed
    logger.error(f"❌ All {max_retries + 1} sticker attempts failed. Last error: {last_error}")
    raise last_error

async def send_photo_with_retry(bot: Bot, chat_id: str, photo, caption: str = None, parse_mode: str = None, max_retries: int = 3, timeout: int = 60, reply_to_message_id: str = None):
    """
    Send a photo with retry logic for handling timeouts and network errors

    Args:
        bot (Bot): Telegram bot instance
        chat_id (str): Chat ID to send the photo to
        photo: Photo to send (file path, URL, or file object)
        caption (str, optional): Photo caption
        parse_mode (str, optional): Parse mode for the caption
        max_retries (int): Maximum number of retry attempts
        timeout (int): Timeout in seconds for each attempt (default 60 for photos)
        reply_to_message_id (str, optional): Message ID to reply to

    Returns:
        Message: Sent message object

    Raises:
        TelegramError: If all retry attempts fail
    """
    last_error = None

    for attempt in range(max_retries + 1):
        try:
            logger.info(f"📸 Sending photo (attempt {attempt + 1}/{max_retries + 1})")

            # Prepare send parameters with timeouts
            send_params = {
                "chat_id": chat_id,
                "photo": photo,
                "read_timeout": timeout,
                "write_timeout": timeout,
                "connect_timeout": timeout,
                "pool_timeout": timeout
            }

            if caption:
                send_params["caption"] = caption
            if parse_mode:
                send_params["parse_mode"] = parse_mode

            # Add reply_to_message_id if provided
            if reply_to_message_id:
                try:
                    reply_id = int(reply_to_message_id.split(',')[0])
                    if reply_id > 0:
                        send_params["reply_to_message_id"] = reply_id
                        logger.info(f"🔗 Setting reply_to_message_id: {reply_id}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid reply_to_message_id: {reply_to_message_id}, ignoring")

            # Send the photo
            message = await bot.send_photo(**send_params)
            logger.info(f"✅ Photo sent successfully on attempt {attempt + 1}")
            return message

        except TimedOut as e:
            last_error = e
            logger.warning(f"⏰ Photo timeout on attempt {attempt + 1}: {e}")
            if attempt < max_retries:
                wait_time = (attempt + 1) * 3  # Longer wait for photo timeouts
                logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)

        except RetryAfter as e:
            last_error = e
            logger.warning(f"🚫 Rate limited, waiting {e.retry_after} seconds")
            await asyncio.sleep(e.retry_after)
            # Don't count rate limit as a retry attempt
            continue

        except NetworkError as e:
            last_error = e
            logger.warning(f"🌐 Network error on attempt {attempt + 1}: {e}")
            if attempt < max_retries:
                wait_time = (attempt + 1) * 4  # Even longer wait for network issues with photos
                logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)

        except TelegramError as e:
            # Check for specific errors we can handle
            error_message = str(e).lower()
            if "message to be replied not found" in error_message and reply_to_message_id:
                # Handle reply message not found - retry without reply
                logger.warning(f"⚠️ Reply message not found, retrying photo without reply: {e}")
                try:
                    # Retry immediately without reply_to_message_id
                    send_params_no_reply = {
                        "chat_id": chat_id,
                        "photo": photo,
                        "read_timeout": timeout,
                        "write_timeout": timeout,
                        "connect_timeout": timeout,
                        "pool_timeout": timeout
                    }
                    if caption:
                        send_params_no_reply["caption"] = caption
                    if parse_mode:
                        send_params_no_reply["parse_mode"] = parse_mode

                    message = await bot.send_photo(**send_params_no_reply)
                    logger.info(f"✅ Photo sent successfully without reply after reply error")
                    return message
                except Exception as retry_error:
                    logger.error(f"❌ Failed to send photo even without reply: {retry_error}")
                    raise retry_error
            else:
                # For other telegram errors, don't retry
                logger.error(f"❌ Telegram error (not retrying): {e}")
                raise e

    # All retries failed
    logger.error(f"❌ All {max_retries + 1} photo attempts failed. Last error: {last_error}")
    raise last_error

async def send_message_with_retry(bot: Bot, chat_id: str, text: str, parse_mode: str = None, max_retries: int = 3, timeout: int = 30, reply_to_message_id: str = None):
    """
    Send a message with retry logic for handling timeouts and network errors

    Args:
        bot (Bot): Telegram bot instance
        chat_id (str): Chat ID to send the message to
        text (str): Message text to send
        parse_mode (str, optional): Parse mode for the message
        max_retries (int): Maximum number of retry attempts
        timeout (int): Timeout in seconds for each attempt
        reply_to_message_id (str, optional): Message ID to reply to

    Returns:
        Message: Sent message object

    Raises:
        TelegramError: If all retry attempts fail
    """
    last_error = None

    for attempt in range(max_retries + 1):
        try:
            logger.info(f"📤 Sending message attempt {attempt + 1}/{max_retries + 1} to {chat_id}")

            # Prepare send_message parameters
            send_params = {
                "chat_id": chat_id,
                "text": text,
                "parse_mode": parse_mode,
                "disable_web_page_preview": True,
                "read_timeout": timeout,
                "write_timeout": timeout,
                "connect_timeout": timeout,
                "pool_timeout": timeout
            }

            # Add reply_to_message_id if provided
            if reply_to_message_id:
                try:
                    # Validate and convert reply_to_message_id
                    reply_id = int(reply_to_message_id)
                    if reply_id <= 0:
                        logger.warning(f"Invalid reply_to_message_id (non-positive): {reply_to_message_id}, ignoring")
                    else:
                        send_params["reply_to_message_id"] = reply_id
                        logger.info(f"🔗 Setting reply_to_message_id: {reply_id}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid reply_to_message_id format: {reply_to_message_id}, ignoring")

            message = await bot.send_message(**send_params)

            logger.info(f"✅ Message sent successfully on attempt {attempt + 1}")
            return message

        except TimedOut as e:
            last_error = e
            logger.warning(f"⏰ Timeout on attempt {attempt + 1}: {e}")
            if attempt < max_retries:
                wait_time = (attempt + 1) * 2  # Exponential backoff
                logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)

        except RetryAfter as e:
            last_error = e
            logger.warning(f"🚫 Rate limited, waiting {e.retry_after} seconds")
            await asyncio.sleep(e.retry_after)
            # Don't count rate limit as a retry attempt
            continue

        except NetworkError as e:
            last_error = e
            logger.warning(f"🌐 Network error on attempt {attempt + 1}: {e}")
            if attempt < max_retries:
                wait_time = (attempt + 1) * 3  # Longer wait for network issues
                logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)

        except TelegramError as e:
            # Check for specific errors we can handle
            error_message = str(e).lower()
            if "message to be replied not found" in error_message and reply_to_message_id:
                # Handle reply message not found - retry without reply immediately
                logger.warning(f"⚠️ Reply message not found, retrying without reply: {e}")
                try:
                    # Retry immediately without reply_to_message_id
                    send_params_no_reply = {
                        "chat_id": chat_id,
                        "text": text,
                        "parse_mode": parse_mode,
                        "read_timeout": timeout,
                        "write_timeout": timeout,
                        "connect_timeout": timeout,
                        "pool_timeout": timeout
                    }
                    message = await bot.send_message(**send_params_no_reply)
                    logger.info(f"✅ Message sent successfully without reply after reply error")
                    return message
                except Exception as retry_error:
                    logger.error(f"❌ Failed to send message even without reply: {retry_error}")
                    raise retry_error
            else:
                # For other telegram errors, don't retry
                logger.error(f"❌ Telegram error (not retrying): {e}")
                raise e

    # All retries failed
    logger.error(f"❌ All {max_retries + 1} attempts failed. Last error: {last_error}")
    raise last_error

async def split_and_send_message(bot: Bot, chat_id: str, text: str, parse_mode: str = None, max_length: int = 4096, reply_to_message_id: str = None):
    """
    Split a long message into smaller chunks and send them sequentially

    Args:
        bot (Bot): Telegram bot instance
        chat_id (str): Chat ID to send the message to
        text (str): Message text to send
        parse_mode (str, optional): Parse mode for the message
        max_length (int, optional): Maximum length of each message chunk
        reply_to_message_id (str, optional): Message ID to reply to

    Returns:
        list: List of sent message objects
    """
    if len(text) <= max_length:
        # Message is short enough, send it as is
        message = await send_message_with_retry(
            bot=bot,
            chat_id=chat_id,
            text=text,
            parse_mode=parse_mode,
            reply_to_message_id=reply_to_message_id
        )
        return [message]

    # Message is too long, split it into chunks
    messages = []
    first_message = True  # Track if this is the first message for reply functionality

    # Try to split at paragraph boundaries first
    paragraphs = text.split('\n\n')
    current_chunk = ""

    for paragraph in paragraphs:
        # If adding this paragraph would exceed max length, send current chunk
        if len(current_chunk) + len(paragraph) + 2 > max_length:
            if current_chunk:
                message = await send_message_with_retry(
                    bot=bot,
                    chat_id=chat_id,
                    text=current_chunk.strip(),
                    parse_mode=parse_mode,
                    reply_to_message_id=reply_to_message_id if first_message else None
                )
                messages.append(message)
                first_message = False
                current_chunk = paragraph + "\n\n"
            else:
                # Single paragraph is too long, need to split it further
                sentences = re.split(r'(?<=[.!?])\s+', paragraph)
                for sentence in sentences:
                    if len(current_chunk) + len(sentence) + 1 > max_length:
                        if current_chunk:
                            message = await send_message_with_retry(
                                bot=bot,
                                chat_id=chat_id,
                                text=current_chunk.strip(),
                                parse_mode=parse_mode,
                                reply_to_message_id=reply_to_message_id if first_message else None
                            )
                            messages.append(message)
                            first_message = False
                            current_chunk = sentence + " "
                        else:
                            # Even a single sentence is too long, split by words
                            words = sentence.split()
                            for word in words:
                                if len(current_chunk) + len(word) + 1 > max_length:
                                    message = await send_message_with_retry(
                                        bot=bot,
                                        chat_id=chat_id,
                                        text=current_chunk.strip(),
                                        parse_mode=parse_mode,
                                        reply_to_message_id=reply_to_message_id if first_message else None
                                    )
                                    messages.append(message)
                                    first_message = False
                                    current_chunk = word + " "
                                else:
                                    current_chunk += word + " "
                    else:
                        current_chunk += sentence + " "
        else:
            current_chunk += paragraph + "\n\n"

    # Send any remaining text
    if current_chunk:
        message = await send_message_with_retry(
            bot=bot,
            chat_id=chat_id,
            text=current_chunk.strip(),
            parse_mode=parse_mode,
            reply_to_message_id=reply_to_message_id if first_message else None
        )
        messages.append(message)

    return messages

async def send_date_sticker_to_channel(chat_id: str, sticker_path: str):
    """
    Send a date sticker to a Telegram channel

    Args:
        chat_id (str): Channel chat ID
        sticker_path (str): Path to the sticker file

    Returns:
        Message: Sent message object or None if failed
    """
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN not set, cannot send sticker")
        return None

    try:
        logger.info(f"🎯 Sending date sticker to channel {chat_id}")
        logger.info(f"📁 Sticker path: {sticker_path}")

        # Verify sticker file exists
        if not os.path.exists(sticker_path):
            logger.error(f"❌ Sticker file not found: {sticker_path}")
            return None

        # Create bot instance
        bot = Bot(token=TELEGRAM_BOT_TOKEN)

        # Send sticker
        with open(sticker_path, "rb") as sticker_file:
            message = await send_sticker_with_retry(
                bot=bot,
                chat_id=chat_id,
                sticker=sticker_file,
                timeout=30
            )

        logger.info(f"✅ Date sticker sent successfully to channel {chat_id}")
        return message

    except Exception as e:
        logger.error(f"❌ Error sending date sticker to channel {chat_id}: {e}")
        return None

async def send_message_to_channel(chat_id: str, text: str, parse_mode: str = "HTML", reply_to_message_id: str = None):
    """
    Send a message to a Telegram channel

    Args:
        chat_id (str): Channel chat ID
        text (str): Message text to send
        parse_mode (str): Parse mode for formatting (default: HTML)
        reply_to_message_id (str): Message ID to reply to (optional)

    Returns:
        Message: Sent message object or None if failed
    """
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN not set, cannot send message")
        return None

    try:
        logger.info(f"📤 Sending message to channel {chat_id}")
        logger.info(f"📝 Message length: {len(text)}")
        logger.info(f"🎨 Parse mode: {parse_mode}")
        logger.info(f"📋 Message preview: {text[:300]}..." if len(text) > 300 else f"📋 Message: {text}")

        # Create bot instance
        bot = Bot(token=TELEGRAM_BOT_TOKEN)

        # Send message using the split_and_send_message function
        messages = await split_and_send_message(
            bot=bot,
            chat_id=chat_id,
            text=text,
            parse_mode=parse_mode,
            reply_to_message_id=reply_to_message_id
        )

        logger.info(f"✅ Message sent successfully to channel {chat_id}, {len(messages)} message(s) sent")

        # Return the first message
        return messages[0] if messages else None

    except Exception as e:
        logger.error(f"Error sending message to channel {chat_id}: {e}")
        return None

async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle the /start command"""
    await update.message.reply_text(
        "Hello! I'm MignalyBot, an AI-powered trading and analysis content creator. "
        "I'll post regular updates to configured channels with market analysis, "
        "trading signals, news, and economic events."
    )

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle the /help command"""
    help_text = (
        "MignalyBot Commands:\n\n"
        "/start - Start the bot\n"
        "/help - Show this help message\n"
        "/status - Show bot status\n\n"
        "For admin access, please use the web admin interface."
    )
    await update.message.reply_text(help_text)

async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle the /status command"""
    async for db in get_async_db():
        # Get channel count
        if is_sqlite_db():
            channels_result = db.execute(select(Channel).where(Channel.active == True))
        else:
            channels_result = await db.execute(select(Channel).where(Channel.active == True))

        active_channels = channels_result.scalars().all()
        channel_count = len(active_channels)

        # Get post count for last 24 hours
        now = datetime.now(timezone.utc)
        yesterday = now - timedelta(days=1)

        if is_sqlite_db():
            posts_result = db.execute(
                select(Post).where(
                    Post.status == PostStatus.PUBLISHED,
                    Post.published_time >= yesterday
                )
            )
        else:
            posts_result = await db.execute(
                select(Post).where(
                    Post.status == PostStatus.PUBLISHED,
                    Post.published_time >= yesterday
                )
            )

        recent_posts = posts_result.scalars().all()
        post_count = len(recent_posts)

        status_text = (
            f"MignalyBot Status:\n\n"
            f"Active channels: {channel_count}\n"
            f"Posts in last 24 hours: {post_count}\n"
            f"Current time (UTC): {now.strftime('%Y-%m-%d %H:%M:%S')}"
        )
        await update.message.reply_text(status_text)

async def post_scheduler():
    """Check for scheduled posts and publish them"""
    from src.utils.helpers import get_utc_time, get_current_time, format_datetime_with_timezone, get_timezone, to_utc
    from src.ai_integration.qwen_client import QwenClient
    from src.database.models import EconomicEvent
    import pytz

    while True:
        try:
            logger.info("🚀 Post scheduler starting...")
            bot = Bot(token=TELEGRAM_BOT_TOKEN)
            logger.info("🤖 Bot initialized")

            # Use UTC for database comparisons since scheduled_time should be stored in UTC
            now_utc = get_utc_time()
            # Get local time for logging
            now_local = get_current_time()
            logger.info(f"⏰ Times calculated - UTC: {now_utc}, Local: {now_local}")

            # Log current time for debugging
            logger.info(f"Post scheduler checking for posts at {now_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")

            async for db in get_async_db():
                logger.info("🔍 Querying database for scheduled posts...")

                # Greeting messages are now handled by the regular post system
                # Find all scheduled posts (we'll filter by time after timezone conversion)
                if is_sqlite_db():
                    posts_result = db.execute(
                        select(Post)
                        .options(selectinload(Post.channel))
                        .where(Post.status == PostStatus.SCHEDULED)
                        .order_by(Post.scheduled_time.asc(), Post.type.asc())  # Ensure greetings (earliest time) are sent first
                    )
                else:
                    posts_result = await db.execute(
                        select(Post)
                        .options(selectinload(Post.channel))
                        .where(Post.status == PostStatus.SCHEDULED)
                        .order_by(Post.scheduled_time.asc(), Post.type.asc())  # Ensure greetings (earliest time) are sent first
                    )

                logger.info("📊 Processing query results...")
                posts = posts_result.scalars().all()
                logger.info(f"📋 Query returned {len(posts)} posts from database")

                if posts:
                    logger.info(f"Found {len(posts)} scheduled posts to publish")
                    # Debug: Log details about each post
                    for post in posts:
                        logger.info(f"  Post {post.id}: {post.type.value}, scheduled={post.scheduled_time}, signal={post.signal_id}")
                else:
                    logger.info("No scheduled posts found")

                for post in posts:
                    try:
                        # Handle timezone conversion for scheduled_time
                        scheduled_time = post.scheduled_time
                        if scheduled_time.tzinfo is None:
                            # Naive datetime - assume it's in Tehran timezone (legacy posts)
                            tz = get_timezone()
                            scheduled_time = tz.localize(scheduled_time)
                            scheduled_time_utc = to_utc(scheduled_time)
                            logger.info(f"📅 Converting naive scheduled time {post.scheduled_time} (Tehran) to UTC: {scheduled_time_utc}")
                        else:
                            # Already timezone-aware
                            scheduled_time_utc = to_utc(scheduled_time)

                        # Check if it's time to send this post
                        if scheduled_time_utc > now_utc:
                            logger.debug(f"⏰ Post {post.id} scheduled for {scheduled_time_utc} UTC, not yet time (current: {now_utc})")
                            continue

                        logger.info(f"📤 Processing post {post.id} for channel {post.channel.name} (Chat ID: {post.channel.chat_id})")
                        logger.info(f"📝 Post type: {post.type.value}, Content length: {len(post.content)}")
                        logger.info(f"⏰ Scheduled: {scheduled_time_utc} UTC, Current: {now_utc} UTC")
                        logger.info(f"📢 Channel advertisement settings: enabled={getattr(post.channel, 'enable_advertisement', False)}")

                        # Send the post to the channel
                        try:
                            # Special handling for greeting posts with date stickers
                            if post.type == PostType.GREETING and post.image_path and os.path.exists(post.image_path):
                                logger.info(f"🎯 Sending greeting post with date sticker for post {post.id}")

                                # First, send the date sticker
                                sticker_message = await send_date_sticker_to_channel(
                                    chat_id=post.channel.chat_id,
                                    sticker_path=post.image_path
                                )

                                if sticker_message:
                                    logger.info(f"✅ Date sticker sent successfully for greeting post {post.id}")
                                else:
                                    logger.warning(f"⚠️ Failed to send date sticker for greeting post {post.id}")

                                # Only send greeting text if content is not empty (skip for sticker-only posts)
                                if post.content.strip():
                                    logger.info(f"📝 Sending greeting text message for post {post.id}")
                                    # Wait a moment before sending the greeting message
                                    await asyncio.sleep(2)

                                    formatted_content = format_message_for_channel(post.content, post.channel, "HTML")
                                    messages = await split_and_send_message(
                                        bot=bot,
                                        chat_id=post.channel.chat_id,
                                        text=formatted_content,
                                        parse_mode="HTML"
                                    )
                                    message = messages[0]
                                else:
                                    logger.info(f"⏭️ Skipping text message for post {post.id} - empty content (sticker-only post)")
                                    message = sticker_message

                                # Clean up the temporary sticker file only if no other posts are using it
                                try:
                                    # Check if any other scheduled posts are using the same image_path
                                    if is_sqlite_db():
                                        other_posts_result = db.execute(
                                            select(Post).where(
                                                Post.image_path == post.image_path,
                                                Post.status == PostStatus.SCHEDULED,
                                                Post.id != post.id
                                            )
                                        )
                                    else:
                                        other_posts_result = await db.execute(
                                            select(Post).where(
                                                Post.image_path == post.image_path,
                                                Post.status == PostStatus.SCHEDULED,
                                                Post.id != post.id
                                            )
                                        )

                                    other_posts = other_posts_result.scalars().all()

                                    if not other_posts:
                                        # No other posts using this file, safe to delete
                                        os.remove(post.image_path)
                                        logger.info(f"🗑️ Cleaned up temporary sticker file: {post.image_path}")
                                    else:
                                        logger.info(f"🔄 Keeping sticker file {post.image_path} - {len(other_posts)} other posts still need it")

                                except Exception as cleanup_error:
                                    logger.warning(f"⚠️ Failed to clean up sticker file: {cleanup_error}")

                            # Check for image URL first (preferred method)
                            elif post.image_url:
                                logger.info(f"Sending post {post.id} with image URL: {post.image_url}")

                                # Get a working image URL (test accessibility and fallback if needed)
                                working_image_url = await get_working_image_url(post.image_url)

                                if working_image_url:
                                    logger.info(f"Using working image URL: {working_image_url}")
                                    # For posts with image URLs, check if caption is too long
                                    # Telegram has a 1024 character limit for photo captions
                                    if len(post.content) > 1024:
                                        # For performance posts, truncate the caption instead of splitting
                                        if post.type == PostType.PERFORMANCE:
                                            logger.info(f"Performance post caption too long ({len(post.content)} chars), truncating to fit in single message")
                                            # Truncate content to fit in caption with ellipsis
                                            truncated_content = post.content[:1020] + "..."
                                            formatted_caption = format_message_for_channel(truncated_content, post.channel, "HTML")

                                            try:
                                                send_params = {
                                                    "chat_id": post.channel.chat_id,
                                                    "photo": working_image_url,
                                                    "caption": formatted_caption,
                                                    "parse_mode": "HTML"
                                                }

                                                # Add reply_to_message_id if provided
                                                if post.reply_to_message_id:
                                                    try:
                                                        send_params["reply_to_message_id"] = int(post.reply_to_message_id.split(',')[0])
                                                        logger.info(f"📤 SENDING PERFORMANCE IMAGE WITH TRUNCATED CAPTION AND REPLY: post_id={post.id}")
                                                    except (ValueError, TypeError):
                                                        logger.warning(f"Invalid reply_to_message_id for performance image: {post.reply_to_message_id}")

                                                message = await send_photo_with_retry(
                                                    bot=bot,
                                                    chat_id=post.channel.chat_id,
                                                    photo=working_image_url,
                                                    caption=formatted_caption,
                                                    parse_mode="HTML",
                                                    reply_to_message_id=post.reply_to_message_id,
                                                    timeout=60  # Longer timeout for photos
                                                )
                                                messages = [message]
                                            except Exception as photo_error:
                                                logger.warning(f"Error sending performance photo: {photo_error}")
                                                # Fall back to text-only post
                                                formatted_content = format_message_for_channel(post.content, post.channel, "HTML")
                                                messages = await split_and_send_message(
                                                    bot=bot,
                                                    chat_id=post.channel.chat_id,
                                                    text=formatted_content,
                                                    parse_mode="HTML",
                                                    reply_to_message_id=post.reply_to_message_id
                                                )
                                                message = messages[0]
                                        else:
                                            # For non-performance posts, use the original splitting logic
                                            # First send the image with a brief caption
                                            try:
                                                send_params = {
                                                    "chat_id": post.channel.chat_id,
                                                    "photo": working_image_url,
                                                    "caption": "📊 Image for the following analysis:"
                                                }

                                                # Add reply_to_message_id if provided
                                                if post.reply_to_message_id:
                                                    try:
                                                        send_params["reply_to_message_id"] = int(post.reply_to_message_id.split(',')[0])
                                                        logger.info(f"📤 SENDING IMAGE WITH REPLY: post_id={post.id}, reply_to_message_id={post.reply_to_message_id}")
                                                    except (ValueError, TypeError):
                                                        logger.warning(f"Invalid reply_to_message_id for image: {post.reply_to_message_id}")

                                                image_message = await send_photo_with_retry(
                                                    bot=bot,
                                                    chat_id=post.channel.chat_id,
                                                    photo=working_image_url,
                                                    caption="📊 Image for the following analysis:",
                                                    reply_to_message_id=post.reply_to_message_id,
                                                    timeout=60
                                                )

                                                # Then send the full content as text messages
                                                formatted_content = format_message_for_channel(post.content, post.channel, "HTML")

                                                messages = await split_and_send_message(
                                                    bot=bot,
                                                    chat_id=post.channel.chat_id,
                                                    text=formatted_content,
                                                    parse_mode="HTML",
                                                    reply_to_message_id=post.reply_to_message_id
                                                )

                                                # Use the image message ID for reference
                                                message = image_message
                                                # Combine image message with text messages for proper tracking
                                                messages = [image_message] + messages
                                            except Exception as url_error:
                                                logger.warning(f"Error sending working image URL for post {post.id}: {url_error}")
                                                # Fall back to text-only post
                                                formatted_content = format_message_for_channel(post.content, post.channel, "HTML")

                                                messages = await split_and_send_message(
                                                    bot=bot,
                                                    chat_id=post.channel.chat_id,
                                                    text=formatted_content,
                                                    parse_mode="HTML",
                                                    reply_to_message_id=post.reply_to_message_id
                                                )
                                                message = messages[0]
                                    else:
                                        # Caption is short enough, send as normal
                                        try:
                                            # Apply advertisement footer formatting to caption
                                            formatted_caption = format_message_for_channel(post.content, post.channel, "HTML")

                                            send_params = {
                                                "chat_id": post.channel.chat_id,
                                                "photo": working_image_url,
                                                "caption": formatted_caption,
                                                "parse_mode": "HTML",
                                                "disable_web_page_preview": True
                                            }

                                            # Add reply_to_message_id if provided
                                            if post.reply_to_message_id:
                                                try:
                                                    send_params["reply_to_message_id"] = int(post.reply_to_message_id.split(',')[0])
                                                    logger.info(f"📤 SENDING IMAGE WITH CAPTION AND REPLY: post_id={post.id}, reply_to_message_id={post.reply_to_message_id}")
                                                except (ValueError, TypeError):
                                                    logger.warning(f"Invalid reply_to_message_id for image with caption: {post.reply_to_message_id}")

                                            message = await send_photo_with_retry(
                                                bot=bot,
                                                chat_id=post.channel.chat_id,
                                                photo=working_image_url,
                                                caption=formatted_caption,
                                                parse_mode="HTML",
                                                reply_to_message_id=post.reply_to_message_id,
                                                timeout=60
                                            )
                                            messages = [message]  # Ensure messages is always defined
                                        except Exception as photo_error:
                                            logger.warning(f"Error sending photo with working URL for post {post.id}: {photo_error}")
                                            # If working URL still fails, fall back to text-only post
                                            formatted_content = format_message_for_channel(post.content, post.channel, "HTML")

                                            messages = await split_and_send_message(
                                                bot=bot,
                                                chat_id=post.channel.chat_id,
                                                text=formatted_content,
                                                parse_mode="HTML",
                                                reply_to_message_id=post.reply_to_message_id
                                            )
                                            message = messages[0]
                                else:
                                    logger.warning(f"No working image URL available for post {post.id}, sending text-only")
                                    # No working image available, send text-only
                                    formatted_content = format_message_for_channel(post.content, post.channel, "HTML")

                                    messages = await split_and_send_message(
                                        bot=bot,
                                        chat_id=post.channel.chat_id,
                                        text=formatted_content,
                                        parse_mode="HTML",
                                        reply_to_message_id=post.reply_to_message_id
                                    )
                                    message = messages[0]
                            # Fall back to local image path if URL is not available
                            elif post.image_path and os.path.exists(post.image_path):
                                # For posts with local images, check if caption is too long
                                if len(post.content) > 1024:
                                    # For performance posts, truncate the caption instead of splitting
                                    if post.type == PostType.PERFORMANCE:
                                        logger.info(f"Performance post with local image caption too long ({len(post.content)} chars), truncating to fit in single message")
                                        # Truncate content to fit in caption with ellipsis
                                        truncated_content = post.content[:1020] + "..."
                                        formatted_caption = format_message_for_channel(truncated_content, post.channel, "HTML")

                                        try:
                                            send_params = {
                                                "chat_id": post.channel.chat_id,
                                                "caption": formatted_caption,
                                                "parse_mode": "HTML"
                                            }

                                            # Add reply_to_message_id if provided
                                            if post.reply_to_message_id:
                                                try:
                                                    send_params["reply_to_message_id"] = int(post.reply_to_message_id.split(',')[0])
                                                    logger.info(f"📤 SENDING PERFORMANCE LOCAL IMAGE WITH TRUNCATED CAPTION AND REPLY: post_id={post.id}")
                                                except (ValueError, TypeError):
                                                    logger.warning(f"Invalid reply_to_message_id for performance local image: {post.reply_to_message_id}")

                                            # Validate image file before sending
                                            if not validate_image_file(post.image_path):
                                                logger.error(f"Invalid image file for post {post.id}: {post.image_path}")
                                                raise Exception(f"File must be non-empty: {post.image_path}")

                                            with open(post.image_path, "rb") as photo:
                                                message = await send_photo_with_retry(
                                                    bot=bot,
                                                    chat_id=post.channel.chat_id,
                                                    photo=photo,
                                                    caption=formatted_caption,
                                                    parse_mode="HTML",
                                                    reply_to_message_id=post.reply_to_message_id,
                                                    timeout=60
                                                )
                                            messages = [message]
                                        except Exception as photo_error:
                                            logger.warning(f"Error sending performance local photo: {photo_error}")
                                            # Fall back to text-only post
                                            formatted_content = format_message_for_channel(post.content, post.channel, "HTML")
                                            messages = await split_and_send_message(
                                                bot=bot,
                                                chat_id=post.channel.chat_id,
                                                text=formatted_content,
                                                parse_mode="HTML",
                                                reply_to_message_id=post.reply_to_message_id
                                            )
                                            message = messages[0]
                                    else:
                                        # For non-performance posts, use the original splitting logic
                                        # First send the image with a brief caption
                                        # Validate image file before sending
                                        if not validate_image_file(post.image_path):
                                            logger.error(f"Invalid image file for post {post.id}: {post.image_path}")
                                            raise Exception(f"File must be non-empty: {post.image_path}")

                                        with open(post.image_path, "rb") as photo:
                                            image_message = await send_photo_with_retry(
                                                bot=bot,
                                                chat_id=post.channel.chat_id,
                                                photo=photo,
                                                caption="📊 Image for the following analysis:",
                                                timeout=60
                                            )

                                        # Then send the full content as text messages (skip if content is empty - for sticker posts)
                                        if post.content.strip():
                                            formatted_content = format_message_for_channel(post.content, post.channel, "HTML")

                                            messages = await split_and_send_message(
                                                bot=bot,
                                                chat_id=post.channel.chat_id,
                                                text=formatted_content,
                                                parse_mode="HTML",
                                                reply_to_message_id=post.reply_to_message_id
                                            )

                                            # Use the image message ID for reference
                                            message = image_message
                                            # Combine image message with text messages for proper tracking
                                            messages = [image_message] + messages
                                        else:
                                            # For sticker posts with empty content, only send the image
                                            logger.info(f"⏭️ Skipping text message for post {post.id} - empty content (sticker post)")
                                            message = image_message
                                            messages = [image_message]
                                else:
                                    # Caption is short enough, send as normal
                                    try:
                                        # Apply advertisement footer formatting to caption (skip if content is empty - for sticker posts)
                                        if post.content.strip():
                                            formatted_caption = format_message_for_channel(post.content, post.channel, "HTML")
                                        else:
                                            formatted_caption = ""  # Empty content for sticker posts

                                        with open(post.image_path, "rb") as photo:
                                            send_params = {
                                                "chat_id": post.channel.chat_id,
                                                "photo": photo,
                                                "caption": formatted_caption,
                                                "parse_mode": "HTML"
                                            }

                                            # Add reply_to_message_id if provided
                                            if post.reply_to_message_id:
                                                try:
                                                    send_params["reply_to_message_id"] = int(post.reply_to_message_id.split(',')[0])
                                                    logger.info(f"📤 SENDING LOCAL IMAGE WITH REPLY: post_id={post.id}, reply_to_message_id={post.reply_to_message_id}")
                                                except (ValueError, TypeError):
                                                    logger.warning(f"Invalid reply_to_message_id for local image: {post.reply_to_message_id}")

                                            # Validate image file before sending
                                            if not validate_image_file(post.image_path):
                                                logger.error(f"Invalid image file for post {post.id}: {post.image_path}")
                                                raise Exception(f"File must be non-empty: {post.image_path}")

                                            with open(post.image_path, "rb") as photo:
                                                message = await send_photo_with_retry(
                                                    bot=bot,
                                                    chat_id=post.channel.chat_id,
                                                    photo=photo,
                                                    caption=formatted_caption,
                                                    parse_mode="HTML",
                                                    reply_to_message_id=post.reply_to_message_id,
                                                    timeout=60
                                                )
                                            messages = [message]  # Ensure messages is always defined
                                    except Exception as photo_error:
                                        # If sending with caption fails, try without HTML parsing
                                        logger.warning(f"Error sending local photo for post {post.id}: {photo_error}")

                                        # Only fall back to text-only post if content is not empty (skip for sticker posts)
                                        if post.content.strip():
                                            # Fall back to text-only post
                                            formatted_content = format_message_for_channel(post.content, post.channel, "HTML")

                                            messages = await split_and_send_message(
                                                bot=bot,
                                                chat_id=post.channel.chat_id,
                                                text=formatted_content,
                                                parse_mode="HTML",
                                                reply_to_message_id=post.reply_to_message_id
                                            )
                                            message = messages[0]
                                        else:
                                            # For sticker posts with empty content, mark as failed and skip
                                            logger.error(f"❌ Failed to send sticker for post {post.id} and content is empty - marking as failed")
                                            post.status = PostStatus.FAILED
                                            post.updated_at = now_utc
                                            continue
                            else:
                                # Send text-only post with chunking if needed
                                logger.info(f"Sending post {post.id} as text-only (no image URL)")

                                # Log reply information
                                if post.reply_to_message_id:
                                    logger.info(f"📤 SENDING TEXT MESSAGE WITH REPLY: post_id={post.id}, reply_to_message_id={post.reply_to_message_id}, channel={post.channel.chat_id}")
                                else:
                                    logger.info(f"📤 SENDING TEXT MESSAGE WITHOUT REPLY: post_id={post.id}, channel={post.channel.chat_id}")

                                # Apply advertisement footer formatting (skip if content is empty - for sticker posts)
                                if post.content.strip():
                                    formatted_content = format_message_for_channel(post.content, post.channel, "HTML")

                                    messages = await split_and_send_message(
                                        bot=bot,
                                        chat_id=post.channel.chat_id,
                                        text=formatted_content,
                                        parse_mode="HTML",
                                        reply_to_message_id=post.reply_to_message_id
                                    )

                                    # Use the first message ID for reference
                                    message = messages[0]
                                else:
                                    # For sticker posts with empty content, initialize empty messages list
                                    logger.info(f"⏭️ Skipping text message for post {post.id} - empty content (sticker-only post)")
                                    messages = []
                                    message = None
                        except Exception as send_error:
                            logger.error(f"Error sending post {post.id}: {send_error}")
                            # Fallback to plain text without formatting (skip if content is empty - for sticker posts)
                            if post.content.strip():
                                fallback_content = f"⚠️ Error with formatting. Plain text version:\n\n{post.content}"
                                # Still apply advertisement footer even in fallback
                                formatted_fallback = format_message_for_channel(fallback_content, post.channel, None)

                                messages = await split_and_send_message(
                                    bot=bot,
                                    chat_id=post.channel.chat_id,
                                    text=formatted_fallback,
                                    parse_mode=None,
                                    reply_to_message_id=post.reply_to_message_id
                                )
                                message = messages[0]
                            else:
                                # For sticker posts with empty content in fallback, initialize empty messages list
                                logger.info(f"⏭️ Skipping fallback for post {post.id} - empty content (sticker-only post)")
                                messages = []
                                message = None

                        # Update post status (use UTC for database storage)
                        post.status = PostStatus.PUBLISHED
                        post.published_time = now_utc

                        # Store all message IDs (for chunked messages)
                        if messages and len(messages) > 1:
                            message_ids = [str(msg.message_id) for msg in messages]
                            post.message_id = ",".join(message_ids)
                            logger.info(f"💾 STORED MULTIPLE MESSAGE IDs: {post.message_id} (chunked into {len(messages)} parts)")
                        elif message:
                            post.message_id = str(message.message_id)
                            logger.info(f"💾 STORED SINGLE MESSAGE ID: {post.message_id}")
                        else:
                            # For sticker-only posts with no text messages
                            post.message_id = ""
                            logger.info(f"💾 NO MESSAGE ID TO STORE (sticker-only post): {post.id}")

                        post.updated_at = now_utc

                        logger.info(f"Published post {post.id} to channel {post.channel.name}")
                    except Exception as e:
                        logger.error(f"Failed to publish post {post.id}: {e}")
                        post.status = PostStatus.FAILED
                        post.updated_at = now_utc

                # Commit changes
                if is_sqlite_db():
                    db.commit()
                else:
                    await db.commit()

        except Exception as e:
            logger.error(f"❌ Error in post scheduler: {e}", exc_info=True)
            logger.error(f"❌ Exception type: {type(e).__name__}")
            logger.error(f"❌ Exception args: {e.args}")

        # Check every minute
        await asyncio.sleep(60)

async def missed_posts_handler():
    """Handle posts that were missed due to scheduler downtime"""
    from src.utils.helpers import get_utc_time

    while True:
        try:
            logger.info("🔍 Checking for missed posts...")
            now_utc = get_utc_time()

            # Look for posts that should have been sent but are still scheduled
            # Consider posts missed if they're more than 5 minutes overdue
            missed_threshold = now_utc - timedelta(minutes=5)

            async for db in get_async_db():
                if is_sqlite_db():
                    missed_posts_result = db.execute(
                        select(Post)
                        .options(selectinload(Post.channel))
                        .where(
                            Post.status == PostStatus.SCHEDULED,
                            Post.scheduled_time <= missed_threshold
                        )
                        .order_by(Post.scheduled_time.asc())
                        .limit(10)  # Process max 10 missed posts at a time
                    )
                else:
                    missed_posts_result = await db.execute(
                        select(Post)
                        .options(selectinload(Post.channel))
                        .where(
                            Post.status == PostStatus.SCHEDULED,
                            Post.scheduled_time <= missed_threshold
                        )
                        .order_by(Post.scheduled_time.asc())
                        .limit(10)  # Process max 10 missed posts at a time
                    )

                missed_posts = missed_posts_result.scalars().all()

                if missed_posts:
                    logger.info(f"📋 Found {len(missed_posts)} missed posts, rescheduling them...")

                    for post in missed_posts:
                        # Reschedule missed posts to be sent immediately
                        post.scheduled_time = now_utc + timedelta(seconds=random.randint(10, 60))
                        logger.info(f"⏰ Rescheduled missed post {post.id} to {post.scheduled_time}")

                    if is_sqlite_db():
                        db.commit()
                    else:
                        await db.commit()

                    logger.info(f"✅ Rescheduled {len(missed_posts)} missed posts")
                else:
                    logger.debug("No missed posts found")

        except Exception as e:
            logger.error(f"❌ Error in missed posts handler: {e}", exc_info=True)

        # Check for missed posts every 5 minutes
        await asyncio.sleep(300)

async def performance_update_scheduler():
    """Generate performance updates every 15 minutes"""
    from src.ai_integration.signal_update_processor import process_signal_updates

    while True:
        try:
            logger.info("📊 Running 15-minute performance update check...")

            # Process signal updates (this will generate performance posts)
            await process_signal_updates()

            logger.info("✅ Performance update check completed")

        except Exception as e:
            logger.error(f"❌ Error in performance update scheduler: {e}", exc_info=True)

        # Run every 15 minutes
        await asyncio.sleep(15 * 60)

async def start_telegram_bot():
    """Start the Telegram bot with enhanced error handling and retry logic"""
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN not set in environment variables")
        return

    max_retries = 5
    retry_delay = 10  # Start with 10 seconds

    for attempt in range(max_retries):
        try:
            logger.info(f"🤖 Starting Telegram bot (attempt {attempt + 1}/{max_retries})")

            # Create the Application with enhanced error handling
            application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()

            # Add command handlers
            application.add_handler(CommandHandler("start", start_command))
            application.add_handler(CommandHandler("help", help_command))
            application.add_handler(CommandHandler("status", status_command))

            # Start background tasks
            asyncio.create_task(post_scheduler())
            asyncio.create_task(missed_posts_handler())
            asyncio.create_task(performance_update_scheduler())

            # Initialize and start the bot
            await application.initialize()
            await application.start()

            # Start polling with error handling
            logger.info("🚀 Starting Telegram bot polling...")
            await application.updater.start_polling(
                drop_pending_updates=True,  # Drop pending updates on restart
                allowed_updates=None,  # Allow all update types
                timeout=30,  # 30 second timeout for long polling
                bootstrap_retries=5,  # Increased retry bootstrap attempts
                read_timeout=30,  # Read timeout for requests
                write_timeout=30,  # Write timeout for requests
                connect_timeout=30,  # Connection timeout
                pool_timeout=30  # Pool timeout
            )

            logger.info("✅ Telegram bot started successfully")

            # Keep the bot running with error recovery
            try:
                while True:
                    await asyncio.sleep(60)

                    # Check if updater is still running
                    if not application.updater.running:
                        logger.warning("⚠️ Telegram updater stopped unexpectedly, attempting restart...")
                        raise NetworkError("Updater stopped unexpectedly")

            except (NetworkError, ConnectionError, TimeoutError, httpx.ConnectError) as e:
                logger.error(f"🌐 Network error in Telegram bot: {e}")
                raise  # Re-raise to trigger retry logic

            except Exception as e:
                logger.error(f"❌ Unexpected error in Telegram bot: {e}", exc_info=True)
                raise

        except (NetworkError, ConnectionError, TimeoutError, httpx.ConnectError) as e:
            error_type = type(e).__name__
            logger.error(f"🌐 Network error ({error_type}) on attempt {attempt + 1}: {e}")

            # Special handling for specific error types
            if "All connection attempts failed" in str(e):
                logger.warning("🔌 All connection attempts failed - likely network connectivity issue")
                retry_delay = min(retry_delay * 1.5, 180)  # Slower backoff for connection issues
            elif "ConnectError" in error_type:
                logger.warning("🔗 Connection error - checking network connectivity")
                retry_delay = min(retry_delay * 2, 300)  # Standard exponential backoff

            if attempt < max_retries - 1:
                logger.info(f"⏳ Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
                continue
            else:
                logger.error("❌ All retry attempts failed for Telegram bot")
                logger.error("💡 Consider checking network connectivity and Telegram API status")
                raise

        except Exception as e:
            logger.error(f"❌ Critical error in Telegram bot: {e}", exc_info=True)

            if attempt < max_retries - 1:
                logger.info(f"⏳ Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
                retry_delay = min(retry_delay * 2, 300)
                continue
            else:
                raise

        finally:
            # Cleanup
            try:
                if 'application' in locals():
                    await application.stop()
                    await application.updater.stop()
                    logger.info("🛑 Telegram bot stopped")
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {cleanup_error}")

        # If we reach here, the bot started successfully and is running
        break
