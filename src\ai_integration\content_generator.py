"""
Content generation module for MignalyBot
"""

import os
import logging
import httpx
from datetime import datetime, timedelta, timezone
import random
import asyncio
from sqlalchemy import select
import pytz
import hashlib

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import (
    Config, Channel, NewsItem, EconomicEvent, TradingSignal as Signal,
    Post, PostType, PostStatus, SignalStatus, CandleData
)
from src.ai_integration.qwen_client import QwenClient
from src.utils.helpers import get_timezone, get_current_time, get_utc_time
from src.data_collection.economic_calendar import collect_events_for_greeting
from src.image_generation.binance_pnl_generator import BinancePnLGenerator
from src.image_generation.date_sticker_generator import DateStickerGenerator

# Multi-language captions for performance updates and common terms
PERFORMANCE_CAPTIONS = {
    'en': {
        'tp_hit': 'TP{level} HIT! 🎯',
        'tp1_hit_break_even': 'TP1 HIT! 🎯\n🔒 Break Even Now',
        'break_even': '⚖️ BREAK EVEN\n🔒 Trade Closed at Entry Price',
        'trade_result': '📊 TRADE RESULT',
        'performance_update': '📊 PERFORMANCE UPDATE',
        'pips_profit': '+{pips} pips profit',
        'pips_loss': '{pips} pips loss',
        'final_result': '🏁 FINAL RESULT',
        'soon': 'soon'
    },
    'fa': {
        'tp_hit': 'هدف {level} رسید! 🎯',
        'tp1_hit_break_even': 'هدف 1 رسید! 🎯\n🔒 ریسک فری کنید',
        'break_even': '⚖️ سر به سر\n🔒 معامله در قیمت ورود بسته شد',
        'trade_result': '📊 نتیجه معامله',
        'performance_update': '📊 بروزرسانی عملکرد',
        'pips_profit': '+{pips} پیپ سود',
        'pips_loss': '{pips} پیپ ضرر',
        'final_result': '🏁 نتیجه نهایی',
        'soon': 'چند دقیقه دیگر'
    },
    'ar': {
        'tp_hit': 'تم الوصول للهدف {level}! 🎯',
        'tp1_hit_break_even': 'تم الوصول للهدف 1! 🎯\n🔒 أصبحت في نقطة التعادل',
        'break_even': '⚖️ نقطة التعادل\n🔒 تم إغلاق الصفقة عند سعر الدخول',
        'trade_result': '📊 نتيجة التداول',
        'performance_update': '📊 تحديث الأداء',
        'pips_profit': '+{pips} نقطة ربح',
        'pips_loss': '{pips} نقطة خسارة',
        'final_result': '🏁 النتيجة النهائية',
        'soon': 'قريباً'
    }
}

def is_forex_pair(symbol):
    """Check if symbol is a forex pair (excluding indices/commodities that don't use pips)"""
    # Normalize symbol by removing slashes and converting to uppercase
    normalized_symbol = symbol.upper().replace('/', '')

    base_forex_pairs = [
        # Major forex pairs
        'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
        # Cross pairs
        'EURJPY', 'GBPJPY', 'EURGBP', 'EURAUD', 'EURCHF', 'AUDCHF', 'GBPCHF',
        'CADCHF', 'NZDCHF', 'NZDJPY', 'AUDCAD', 'AUDNZD', 'CADJPY', 'CHFJPY',
        'EURNZD', 'EURCAD', 'GBPAUD', 'GBPCAD', 'GBPNZD', 'NZDCAD','XAUUSD','DJIUSD'
        # Note: DJIUSD and XAUUSD removed - they don't use pip calculations
    ]
    # Add both original and slash format
    forex_pairs = base_forex_pairs + [f"{p[:3]}/{p[3:]}" for p in base_forex_pairs]
    return any(pair == normalized_symbol for pair in forex_pairs)

def calculate_pips(entry_price, exit_price, symbol):
    """Calculate pips for forex symbols only"""
    if not entry_price or not exit_price:
        return 0

    # Only calculate pips for forex pairs
    if not is_forex_pair(symbol):
        return 0

    # Different pip values for different instrument types
    symbol_upper = symbol.upper().replace('/', '')

    # Special handling for DJIUSD and XAUUSD
    if symbol_upper == 'DJIUSD':
        # For DJIUSD: 1 pip = 1 point (no division)
        pip_value = 1.0
    elif symbol_upper == 'XAUUSD':
        # For XAUUSD: 1 pip = 1 point (no division)
        pip_value = 1.0
    elif 'JPY' in symbol_upper:
        # JPY pairs: 1 pip = 0.01
        pip_value = 0.01
    else:
        # Standard forex pairs: 1 pip = 0.0001
        pip_value = 0.0001

    price_diff = abs(exit_price - entry_price)
    pips = price_diff / pip_value
    return round(pips, 1)

def _is_response_complete(content):
    """
    Check if AI response appears to be complete

    Args:
        content (str): AI generated content

    Returns:
        bool: True if response seems complete, False otherwise
    """
    if not content or not content.strip():
        return False

    content = content.strip()

    # Check minimum length
    if len(content) < 20:
        return False

    # Check for abrupt endings that suggest truncation
    truncation_indicators = [
        '...',  # Ellipsis
        ',',    # Ends with comma
        ':',    # Ends with colon
        '{',    # Incomplete JSON
        '"',    # Incomplete quote
        'and',  # Ends with conjunction
        'the',  # Ends with article
        'to',   # Ends with preposition
        'is',   # Ends with verb
        'are',  # Ends with verb
    ]

    # Check if content ends with any truncation indicators
    for indicator in truncation_indicators:
        if content.endswith(indicator):
            return False

    # Check for incomplete sentences (no ending punctuation)
    if not content.endswith(('.', '!', '?', '🎯', '📈', '📉', '💰', '🚀')):
        return False

    return True

def get_performance_caption(caption_type, language='en', **kwargs):
    """
    Get localized performance caption

    Args:
        caption_type (str): Type of caption (tp_hit, trade_result, etc.)
        language (str): Language code (en, fa, ar)
        **kwargs: Additional parameters for formatting (level, pips, etc.)

    Returns:
        str: Localized caption
    """
    # Default to English if language not supported
    lang_captions = PERFORMANCE_CAPTIONS.get(language, PERFORMANCE_CAPTIONS['en'])

    # Get the caption template
    caption_template = lang_captions.get(caption_type, PERFORMANCE_CAPTIONS['en'][caption_type])

    # Format with provided parameters
    try:
        return caption_template.format(**kwargs)
    except KeyError as e:
        # If formatting fails, return template as-is
        logger.warning(f"Caption formatting failed for {caption_type} in {language}: {e}")
        return caption_template

logger = logging.getLogger(__name__)


def ensure_channel_attributes(channel):
    """
    Ensure channel has all required attributes with safe defaults

    Args:
        channel: Channel object to validate

    Returns:
        dict: Safe channel attributes
    """
    # Defensive check for channel object
    if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
        logger.error(f"❌ Invalid channel object passed to ensure_channel_attributes: {type(channel)} - {repr(channel)}")
        # Return safe defaults for invalid channel
        return {
            'language': "en",
            'brand_name': "Trading Channel",
            'post_types': "news,signal,analysis,event,performance,greeting"
        }

    safe_attrs = {}

    # Language with fallback
    safe_attrs['language'] = getattr(channel, 'language', None) or "en"

    # Brand name with fallback
    safe_attrs['brand_name'] = getattr(channel, 'brand_name', None) or getattr(channel, 'name', 'Trading Channel')

    # Post types with fallback
    safe_attrs['post_types'] = getattr(channel, 'post_types', None) or "news,signal,analysis,event,performance,greeting"

    return safe_attrs

def generate_pnl_image_from_signal(signal, tp_level=None, tp_hit_record=None):
    """
    Generate a Binance-style PnL image from a trading signal

    Args:
        signal: Signal object
        tp_level: TP level (1, 2, 3) or None for final result
        tp_hit_record: TakeProfitHit record for accurate TP price (optional)

    Returns:
        str: Path to generated image or None if failed
    """
    try:
        logger.info(f"🖼️ Starting PnL image generation from signal {signal.id}")
        generator = BinancePnLGenerator()
        logger.info(f"🐳 Generator Docker mode: {generator.docker_mode}")



        # Helper function to calculate pips for forex
        def calculate_pips(entry_price, exit_price, symbol):
            """Calculate pips for forex symbols"""
            if not entry_price or not exit_price:
                return 0

            symbol_upper = symbol.upper().replace('/', '')

            # Special handling for DJIUSD and XAUUSD
            if symbol_upper == 'DJIUSD':
                # For DJIUSD: 1 pip = 1 point (no division)
                pip_value = 1.0
            elif symbol_upper == 'XAUUSD':
                # For XAUUSD: 1 pip = 1 point (no division)
                pip_value = 1.0
            elif 'JPY' in symbol_upper:
                # For JPY pairs, 1 pip = 0.01
                pip_value = 0.01
            else:
                # For standard forex pairs, 1 pip = 0.0001
                pip_value = 0.0001

            price_diff = abs(exit_price - entry_price)
            pips = price_diff / pip_value
            return round(pips, 1)

        # Calculate profit/loss based on TP level or signal status
        # Priority 1: Use TP hit record if provided (most accurate)
        if tp_hit_record and hasattr(tp_hit_record, 'tp_price') and signal.entry_price:
            tp_price = tp_hit_record.tp_price
            if signal.direction == "buy":
                profit_loss_pct = (tp_price - signal.entry_price) / signal.entry_price * 100
            else:
                profit_loss_pct = (signal.entry_price - tp_price) / signal.entry_price * 100

            # Apply final 10x multiplier to ALL symbols for PnL image display (consistent across all channels)
            profit_loss_pct *= 10
            logger.info(f"📊 Applied final 10x multiplier for PnL image display")

            logger.info(f"📊 Calculated TP hit profit: {profit_loss_pct:.2f}% (Entry: {signal.entry_price}, TP Hit: {tp_price})")
        # Priority 2: Use TP level from signal
        elif tp_level and hasattr(signal, f'take_profit_{tp_level}'):
            # For TP hits, calculate profit based on the specific TP level
            tp_price = getattr(signal, f'take_profit_{tp_level}')
            if tp_price and signal.entry_price:
                if signal.direction == "buy":
                    profit_loss_pct = (tp_price - signal.entry_price) / signal.entry_price * 100
                else:
                    profit_loss_pct = (signal.entry_price - tp_price) / signal.entry_price * 100

                # Apply forex multiplier if needed (100x instead of 1000x - changed from *100 to *10)
                # REMOVED: forex multiplier - now forex pairs use same multiplier as other symbols
                # if is_forex_pair_for_pnl(signal.symbol):
                #     profit_loss_pct *= 10  # Previously 1000x, now same as others
                #     logger.info(f"📊 Applied forex multiplier for {signal.symbol}")
                logger.info(f"📊 Forex multiplier removed - using standard multiplier for {signal.symbol}")

                # Apply final 10x multiplier to ALL symbols for PnL image display
                profit_loss_pct *= 10
                logger.info(f"📊 Applied final 10x multiplier for PnL image display")

                logger.info(f"📊 Calculated TP{tp_level} profit: {profit_loss_pct:.2f}% (Entry: {signal.entry_price}, TP{tp_level}: {tp_price})")
            else:
                profit_loss_pct = 0.0
        elif signal.profit_loss is not None:
            profit_loss_pct = signal.profit_loss
            # Apply forex multiplier if needed (100x instead of 1000x - changed from *100 to *10)
            # REMOVED: forex multiplier - now forex pairs use same multiplier as other symbols
            # if is_forex_pair_for_pnl(signal.symbol):
            #     profit_loss_pct *= 10
            #     logger.info(f"📊 Applied forex multiplier to existing profit_loss for {signal.symbol}")
            logger.info(f"📊 Forex multiplier removed - using standard multiplier for existing profit_loss {signal.symbol}")

            # Apply final 10x multiplier to ALL symbols for PnL image display
            profit_loss_pct *= 10
            logger.info(f"📊 Applied final 10x multiplier for PnL image display")
        else:
            # Calculate from entry and exit prices
            if signal.exit_price and signal.entry_price:
                if signal.direction == "buy":
                    profit_loss_pct = (signal.exit_price - signal.entry_price) / signal.entry_price * 100
                else:
                    profit_loss_pct = (signal.entry_price - signal.exit_price) / signal.entry_price * 100

                # Apply forex multiplier if needed (100x instead of 1000x - changed from *100 to *10)
                # REMOVED: forex multiplier - now forex pairs use same multiplier as other symbols
                # if is_forex_pair_for_pnl(signal.symbol):
                #     profit_loss_pct *= 10
                #     logger.info(f"📊 Applied forex multiplier for calculated profit for {signal.symbol}")
                logger.info(f"📊 Forex multiplier removed - using standard multiplier for calculated profit {signal.symbol}")

                # Apply final 10x multiplier to ALL symbols for PnL image display
                profit_loss_pct *= 10
                logger.info(f"📊 Applied final 10x multiplier for PnL image display")
            else:
                profit_loss_pct = 0.0

        # Calculate pips for forex symbols only
        pips = 0
        if is_forex_pair(signal.symbol):
            # Priority 1: Use TP hit record if provided (most accurate)
            if tp_hit_record and hasattr(tp_hit_record, 'tp_price') and signal.entry_price:
                pips = calculate_pips(signal.entry_price, tp_hit_record.tp_price, signal.symbol)
                logger.info(f"📊 Calculated pips from TP hit record: {pips} (Entry: {signal.entry_price}, TP Hit: {tp_hit_record.tp_price})")
            # Priority 2: Use TP level from signal
            elif tp_level and hasattr(signal, f'take_profit_{tp_level}'):
                tp_price = getattr(signal, f'take_profit_{tp_level}')
                if tp_price and signal.entry_price:
                    pips = calculate_pips(signal.entry_price, tp_price, signal.symbol)
                    logger.info(f"📊 Calculated pips for TP{tp_level}: {pips} (Entry: {signal.entry_price}, TP: {tp_price})")
            # Priority 3: Use exit price for final results
            elif signal.exit_price and signal.entry_price:
                pips = calculate_pips(signal.entry_price, signal.exit_price, signal.symbol)
                logger.info(f"📊 Calculated pips from exit: {pips} (Entry: {signal.entry_price}, Exit: {signal.exit_price})")
            else:
                logger.info(f"📊 No pips calculated - missing prices (Entry: {getattr(signal, 'entry_price', None)}, Exit: {getattr(signal, 'exit_price', None)}, TP Hit: {getattr(tp_hit_record, 'tp_price', None) if tp_hit_record else None})")
        else:
            logger.info(f"📊 No pips calculated - not a forex pair: {signal.symbol}")

        # Estimate USDT value (assuming $1000 position size if not specified)
        position_size = getattr(signal, 'position_size', None)
        if position_size is None:
            position_size = 1000  # Default fallback
        profit_loss_usdt = position_size * (profit_loss_pct / 100)

        # Determine exit price based on TP hit record, TP level, or signal exit price
        if tp_hit_record and hasattr(tp_hit_record, 'tp_price'):
            exit_price = tp_hit_record.tp_price
        elif tp_level and hasattr(signal, f'take_profit_{tp_level}'):
            exit_price = getattr(signal, f'take_profit_{tp_level}') or signal.entry_price
        else:
            exit_price = signal.exit_price or signal.entry_price

        # Prepare trade data
        trade_data = {
            'symbol': signal.symbol,
            'direction': signal.direction.upper(),
            'entry_price': signal.entry_price,
            'exit_price': exit_price,
            'profit_loss_pct': profit_loss_pct,
            'profit_loss_usdt': profit_loss_usdt,
            'position_size': position_size,
            'leverage': getattr(signal, 'leverage', None) or 1,
            'date': getattr(signal, 'exit_time', None) or getattr(signal, 'entry_time', None) or signal.created_at,
            'tp_level': tp_level,
            'status': signal.status.value.upper(),
            'pips': pips  # Add pips information for forex symbols
        }

        logger.info(f"📊 Signal data for PnL image: {trade_data}")

        result = generator.generate_pnl_image(trade_data)
        if result:
            logger.info(f"✅ PnL image generated successfully: {result}")
        else:
            logger.error(f"❌ PnL image generation failed for signal {signal.id}")

        return result

    except Exception as e:
        logger.error(f"Error generating PnL image for signal {signal.id}: {e}", exc_info=True)
        return None

async def check_recent_posts(db, channel_id, post_type, hours_back=6):
    """
    Check if there are recent posts of the same type for a channel

    Args:
        db: Database session
        channel_id: Channel ID to check
        post_type: Type of post to check for
        hours_back: How many hours back to check (default: 6)

    Returns:
        int: Number of recent posts found
    """
    try:
        now = get_current_time()
        cutoff_time = now - timedelta(hours=hours_back)

        if is_sqlite_db():
            result = db.execute(
                select(Post)
                .where(
                    Post.channel_id == channel_id,
                    Post.type == post_type,
                    Post.created_at >= cutoff_time
                )
            )
        else:
            result = await db.execute(
                select(Post)
                .where(
                    Post.channel_id == channel_id,
                    Post.type == post_type,
                    Post.created_at >= cutoff_time
                )
            )

        posts = result.scalars().all()
        return len(posts)

    except Exception as e:
        logger.error(f"Error checking recent posts: {e}")
        return 0

async def generate_content():
    """Generate comprehensive daily content for all active channels"""
    logger.info("🚀 Starting comprehensive daily content generation")
    logger.debug("🔍 DEBUG: Content generation function called")

    try:
        # Initialize Qwen client
        logger.debug("🔍 DEBUG: Initializing Qwen client...")
        qwen_client = QwenClient()
        logger.debug("🔍 DEBUG: Qwen client initialized successfully")

        # Get configuration
        logger.debug("🔍 DEBUG: Getting database configuration...")
        async for db in get_async_db():
            # Get global config
            if is_sqlite_db():
                config_result = db.execute(select(Config))
            else:
                config_result = await db.execute(select(Config))
            config = config_result.scalars().first()

            if not config:
                logger.error("❌ No configuration found in database")
                return

            logger.info("📋 Global configuration:")
            logger.info(f"  📰 News enabled: {config.enable_news}")
            logger.info(f"  📊 Signals enabled: {config.enable_signals}")
            logger.info(f"  📅 Calendar enabled: {config.enable_calendar}")

            # Get active channels
            if is_sqlite_db():
                channels_result = db.execute(
                    select(Channel).where(Channel.active == True)
                )
            else:
                channels_result = await db.execute(
                    select(Channel).where(Channel.active == True)
                )
            channels = channels_result.scalars().all()

            if not channels:
                logger.warning("⚠️ No active channels found")
                return

            logger.info(f"📺 Generating content for {len(channels)} active channels")

            # Process each channel
            total_posts_created = 0
            for channel in channels:
                try:
                    # Debug: Log channel object details
                    logger.info(f"🔍 DEBUG: Processing channel object: {type(channel)} - {repr(channel)}")
                    logger.info(f"🔍 DEBUG: Channel has 'id': {hasattr(channel, 'id')}")
                    logger.info(f"🔍 DEBUG: Channel has 'name': {hasattr(channel, 'name')}")

                    # Defensive check for channel object
                    if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
                        logger.error(f"❌ Invalid channel object in main loop: {type(channel)} - {repr(channel)}")
                        continue

                    # Ensure channel has required attributes with defaults
                    if not hasattr(channel, 'language') or not channel.language:
                        channel.language = "en"  # Default to English
                        logger.warning(f"Channel {channel.name} missing language, defaulting to 'en'")

                    if not hasattr(channel, 'brand_name') or not channel.brand_name:
                        channel.brand_name = channel.name  # Use channel name as brand name
                        logger.warning(f"Channel {channel.name} missing brand_name, using channel name")

                    if not hasattr(channel, 'post_types') or not channel.post_types:
                        channel.post_types = "news,signal,analysis,event,performance,greeting"  # Default post types
                        logger.warning(f"Channel {channel.name} missing post_types, using defaults")

                    logger.info(f"🔄 Processing channel: {channel.name}")
                    logger.info(f"  Language: {channel.language}")
                    logger.info(f"  Brand: {channel.brand_name}")
                    logger.info(f"  Post types: {channel.post_types}")

                    posts_created = await generate_channel_content(db, channel, qwen_client, config)
                    total_posts_created += posts_created

                    logger.info(f"✅ Created {posts_created} posts for {channel.name}")

                except Exception as channel_error:
                    # Safely get channel name for error logging
                    channel_name = getattr(channel, 'name', str(channel))
                    logger.error(f"❌ Error processing channel {channel_name}: {channel_error}", exc_info=True)
                    continue  # Continue with next channel

            logger.info(f"🎉 Content generation completed! Total posts created: {total_posts_created}")

    except Exception as e:
        logger.error(f"❌ Error in content generation: {e}", exc_info=True)

async def generate_channel_content(db, channel, qwen_client, config):
    """
    Generate comprehensive content for a specific channel

    Args:
        db: Database session
        channel (Channel): Channel to generate content for
        qwen_client (QwenClient): Qwen client for AI generation
        config (Config): Global configuration

    Returns:
        int: Number of posts created
    """
    logger.info(f"📺 Generating comprehensive content for channel: {channel.name}")

    posts_created = 0
    try:
        # Get enabled post types for this channel
        post_types = set(channel.post_types.split(",")) if channel.post_types else set()
        logger.info(f"  📝 Enabled post types: {', '.join(post_types)}")

        # NOTE: Greeting posts are now handled by a separate scheduler at 11 AM Tehran time
        # This ensures greeting posts are sent at the right time, not during data collection cycles

        # 1. Generate news posts
        if "news" in post_types and config.enable_news:
            logger.info("  📰 Generating news posts...")
            news_count = await generate_news_posts(db, channel, qwen_client)
            posts_created += news_count
            logger.info(f"  ✅ Created {news_count} news posts")
        else:
            logger.info(f"  ⏭️ News posts disabled (in post_types: {'news' in post_types}, globally enabled: {config.enable_news})")

        # 2. Generate event posts
        if "event" in post_types and config.enable_calendar:
            logger.info("  📅 Generating event posts...")
            event_count = await generate_event_posts(db, channel, qwen_client)
            posts_created += event_count
            logger.info(f"  ✅ Created {event_count} event posts")
        else:
            logger.info(f"  ⏭️ Event posts disabled (in post_types: {'event' in post_types}, globally enabled: {config.enable_calendar})")

        # 3. Generate signal posts
        if "signal" in post_types and config.enable_signals:
            logger.info("  📊 Generating signal posts...")
            signal_count = await generate_signal_posts(db, channel, qwen_client)
            posts_created += signal_count
            logger.info(f"  ✅ Created {signal_count} signal posts")
        else:
            logger.info(f"  ⏭️ Signal posts disabled (in post_types: {'signal' in post_types}, globally enabled: {config.enable_signals})")

        # 3.5. Generate TP hit posts (immediate updates for partial TP hits)
        # NOTE: TP hit posts and performance updates are now handled by the event result scheduler
        # This ensures immediate updates when signal status changes, rather than waiting for 6-hour cycles
        if "performance" in post_types and config.enable_signals:
            logger.info("  🎯 TP hit posts and performance updates now handled by event result scheduler (every 15 minutes)")
        else:
            logger.info(f"  ⏭️ Performance posts disabled (in post_types: {'performance' in post_types}, globally enabled: {config.enable_signals})")

        # 5. Generate analysis posts
        if "analysis" in post_types:
            logger.info("  📊 Generating analysis posts...")
            analysis_count = await generate_analysis_posts(db, channel, qwen_client, config)
            posts_created += analysis_count
            logger.info(f"  ✅ Created {analysis_count} analysis posts")
        else:
            logger.info("  ⏭️ Analysis posts disabled")

        logger.info(f"✅ Content generation for channel {channel.name} completed - {posts_created} posts created")
        return posts_created

    except Exception as e:
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"❌ Error generating content for channel {channel_name}: {e}", exc_info=True)
        return posts_created

async def generate_news_posts(db, channel, qwen_client):
    """
    Generate news posts for a channel

    Args:
        db: Database session
        channel (Channel): Channel to generate content for
        qwen_client (QwenClient): Qwen client for AI generation

    Returns:
        int: Number of news posts created
    """
    try:
        # Defensive check for channel object
        if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
            logger.error(f"❌ Invalid channel object passed to generate_news_posts: {type(channel)} - {channel}")
            return 0
        # Get recent news that hasn't been posted to this channel
        now = get_current_time()  # For time calculations
        now_utc = get_utc_time()  # For database storage
        yesterday = now - timedelta(days=1)

        # Get news items already posted to this channel
        if is_sqlite_db():
            posted_news_result = db.execute(
                select(Post.news_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.NEWS,
                    Post.news_id.is_not(None)
                )
            )
        else:
            posted_news_result = await db.execute(
                select(Post.news_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.NEWS,
                    Post.news_id.is_not(None)
                )
            )
        posted_news_ids = [row[0] for row in posted_news_result.all()]

        # Get recent news not yet posted
        if is_sqlite_db():
            news_result = db.execute(
                select(NewsItem)
                .where(
                    NewsItem.published_at >= yesterday,
                    ~NewsItem.id.in_(posted_news_ids) if posted_news_ids else True
                )
                .order_by(NewsItem.published_at.desc())
                .limit(3)  # Limit to 3 news items per run
            )
        else:
            news_result = await db.execute(
                select(NewsItem)
                .where(
                    NewsItem.published_at >= yesterday,
                    ~NewsItem.id.in_(posted_news_ids) if posted_news_ids else True
                )
                .order_by(NewsItem.published_at.desc())
                .limit(3)  # Limit to 3 news items per run
            )
        news_items = news_result.scalars().all()

        if not news_items:
            logger.info(f"No new news items to post for channel {channel.name}")
            return 0

        logger.info(f"Generating {len(news_items)} news posts for channel {channel.name}")

        # Process each news item
        for news_item in news_items:
            # Always generate AI analysis tailored for this specific channel
            # This ensures each channel gets content in their language and brand style
            safe_attrs = ensure_channel_attributes(channel)
            channel_specific_content = await qwen_client.analyze_news(
                news_item,
                language=safe_attrs['language'],
                channel_brand=safe_attrs['brand_name']
            )

            # Skip this news item if AI generation failed
            if not channel_specific_content:
                logger.warning(f"Skipping news item '{news_item.title}' for channel {channel.name} - AI generation failed")
                continue

            # DISABLED: Validate response completeness - will be implemented later
            # TODO: Re-implement incomplete response check function later
            # if not _is_response_complete(channel_specific_content):
            #     logger.warning(f"Skipping news item '{news_item.title}' for channel {channel.name} - AI response appears incomplete")
            #     logger.info(f"Incomplete response: {channel_specific_content[:200]}...")
            #     continue

            # Create post with channel-specific content - Schedule after greeting posts
            post = Post(
                channel_id=channel.id,
                type=PostType.NEWS,
                content=channel_specific_content,
                status=PostStatus.SCHEDULED,
                scheduled_time=now_utc + timedelta(minutes=random.randint(1, 300)),  # Random delay up to 5 hours
                news_id=news_item.id,
                image_url=news_item.image_url,  # Use the original image URL directly
                created_at=now_utc  # Use UTC for database storage
            )

            db.add(post)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()
        logger.info(f"Created {len(news_items)} news posts for channel {channel.name}")
        return len(news_items)

    except Exception as e:
        if is_sqlite_db():
            db.rollback()
        else:
            await db.rollback()
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"Error generating news posts for channel {channel_name}: {e}", exc_info=True)
        return 0

async def generate_event_posts(db, channel, qwen_client):
    """
    Generate economic event posts for a channel

    Args:
        db: Database session
        channel (Channel): Channel to generate content for
        qwen_client (QwenClient): Qwen client for AI generation

    Returns:
        int: Number of event posts created
    """
    try:
        # Defensive check for channel object
        if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
            logger.error(f"❌ Invalid channel object passed to generate_event_posts: {type(channel)} - {channel}")
            return 0
        # Get upcoming events in the next 24 hours
        now = get_current_time()  # For time calculations
        now_utc = get_utc_time()  # For database storage
        tomorrow = now + timedelta(days=1)

        # Get events already posted to this channel
        if is_sqlite_db():
            posted_events_result = db.execute(
                select(Post.event_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.EVENT,
                    Post.event_id.is_not(None)
                )
            )
        else:
            posted_events_result = await db.execute(
                select(Post.event_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.EVENT,
                    Post.event_id.is_not(None)
                )
            )
        posted_event_ids = [row[0] for row in posted_events_result.all()]

        # Ensure timezone-aware datetimes for database comparison
        # Convert to UTC for consistent comparison with database stored times
        if now.tzinfo is None:
            now_utc = now.replace(tzinfo=timezone.utc)
        else:
            now_utc = now.astimezone(timezone.utc)

        if tomorrow.tzinfo is None:
            tomorrow_utc = tomorrow.replace(tzinfo=timezone.utc)
        else:
            tomorrow_utc = tomorrow.astimezone(timezone.utc)

        # Get upcoming events not yet posted
        if is_sqlite_db():
            events_result = db.execute(
                select(EconomicEvent)
                .where(
                    EconomicEvent.event_time >= now_utc,
                    EconomicEvent.event_time <= tomorrow_utc,
                    EconomicEvent.impact >= 2,  # Only medium and high impact events
                    ~EconomicEvent.id.in_(posted_event_ids) if posted_event_ids else True
                )
                .order_by(EconomicEvent.event_time)
            )
        else:
            events_result = await db.execute(
                select(EconomicEvent)
                .where(
                    EconomicEvent.event_time >= now_utc,
                    EconomicEvent.event_time <= tomorrow_utc,
                    EconomicEvent.impact >= 2,  # Only medium and high impact events
                    ~EconomicEvent.id.in_(posted_event_ids) if posted_event_ids else True
                )
                .order_by(EconomicEvent.event_time)
            )
        events = events_result.scalars().all()

        if not events:
            logger.info(f"No upcoming economic events to post for channel {channel.name}")
            return 0

        logger.info(f"Generating {len(events)} event posts for channel {channel.name}")

        # Process each event
        for event in events:
            # Always generate AI analysis tailored for this specific channel
            # This ensures each channel gets content in their language and brand style
            safe_attrs = ensure_channel_attributes(channel)
            channel_specific_content = await qwen_client.analyze_economic_event(
                event,
                language=safe_attrs['language'],
                channel_brand=safe_attrs['brand_name']
            )

            # Skip this event if AI generation failed
            if not channel_specific_content:
                logger.warning(f"Skipping event '{event.title}' for channel {channel.name} - AI generation failed")
                continue

            # Create post with channel-specific content - Schedule after greeting posts
            post = Post(
                channel_id=channel.id,
                type=PostType.EVENT,
                content=channel_specific_content,
                status=PostStatus.SCHEDULED,
                scheduled_time=now_utc + timedelta(minutes=random.randint(60, 180)),  # 1-3 hours after greetings
                event_id=event.id,
                created_at=now_utc  # Use UTC for database storage
            )

            db.add(post)

            # Ensure event_time is timezone-aware for comparison and calculation
            # All event times should be stored as UTC in the database
            if event.event_time.tzinfo is None:
                # If event_time is naive, assume it's in UTC (as per our data collection)
                event_time_utc = event.event_time.replace(tzinfo=timezone.utc)
            else:
                # If it's already timezone-aware, convert to UTC
                event_time_utc = event.event_time.astimezone(timezone.utc)

            # Check if we should create a countdown post (if event is more than 2 hours away)
            now_utc = now.astimezone(timezone.utc) if now.tzinfo else now.replace(tzinfo=timezone.utc)
            time_until_event = event_time_utc - now_utc
            hours_until_event = time_until_event.total_seconds() / 3600

            logger.info(f"Event '{event.title}' scheduled for {event_time_utc.strftime('%Y-%m-%d %H:%M:%S UTC')} is {hours_until_event:.2f} hours away")

            if hours_until_event > 2:
                logger.info(f"Creating countdown post for event '{event.title}' (more than 2 hours away)")
                try:
                    safe_attrs = ensure_channel_attributes(channel)
                    countdown_content = await qwen_client.generate_countdown_post(
                        event,
                        language=safe_attrs['language'],
                        channel_brand=safe_attrs['brand_name']
                    )

                    if countdown_content:
                        countdown_post = Post(
                            channel_id=channel.id,
                            type=PostType.EVENT,
                            content=countdown_content,
                            status=PostStatus.SCHEDULED,
                            scheduled_time=event_time_utc - timedelta(hours=1),  # Post 1 hour before event
                            event_id=event.id,
                            created_at=now_utc  # Use UTC for database storage
                        )

                        db.add(countdown_post)
                        logger.info(f"✅ Created countdown post for event '{event.title}' scheduled for {countdown_post.scheduled_time}")
                    else:
                        logger.warning(f"❌ Failed to generate countdown content for event '{event.title}'")
                except Exception as countdown_error:
                    logger.error(f"❌ Error creating countdown post for event '{event.title}': {countdown_error}", exc_info=True)
            else:
                logger.info(f"⏭️ Skipping countdown post for event '{event.title}' (only {hours_until_event:.2f} hours away)")

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()
        # Count posts created (events + potential countdown posts)
        posts_created = len(events) * 2  # Each event can create up to 2 posts (analysis + countdown)
        logger.info(f"Created event posts for channel {channel.name}")
        return posts_created

    except Exception as e:
        if is_sqlite_db():
            db.rollback()
        else:
            await db.rollback()
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"Error generating event posts for channel {channel_name}: {e}", exc_info=True)
        return 0

async def generate_signal_posts(db, channel, qwen_client):
    """
    Generate trading signal posts for a channel

    Args:
        db: Database session
        channel (Channel): Channel to generate content for
        qwen_client (QwenClient): Qwen client for AI generation

    Returns:
        int: Number of signal posts created
    """
    try:
        # Defensive check for channel object
        if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
            logger.error(f"❌ Invalid channel object passed to generate_signal_posts: {type(channel)} - {channel}")
            return 0
        # Get recent signals that haven't been posted to this channel
        now = get_current_time()  # For time calculations
        now_utc = get_utc_time()  # For database storage
        yesterday = now - timedelta(days=1)

        # Get signals already posted to this channel
        if is_sqlite_db():
            posted_signals_result = db.execute(
                select(Post.signal_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.SIGNAL,
                    Post.signal_id.is_not(None)
                )
            )
        else:
            posted_signals_result = await db.execute(
                select(Post.signal_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.SIGNAL,
                    Post.signal_id.is_not(None)
                )
            )
        posted_signal_ids = [row[0] for row in posted_signals_result.all()]

        # Get recent ACTIVE signals not yet posted
        query_conditions = [
            Signal.entry_time >= yesterday,
            Signal.status == SignalStatus.ACTIVE  # Only active signals for signal posts
        ]

        # Only add the exclusion condition if there are actually posted signal IDs
        if posted_signal_ids:
            query_conditions.append(~Signal.id.in_(posted_signal_ids))
            logger.info(f"Excluding {len(posted_signal_ids)} already posted signals")

        if is_sqlite_db():
            signals_result = db.execute(
                select(Signal)
                .where(*query_conditions)
                .order_by(Signal.entry_time.desc())
                .limit(10)  # Limit to prevent too many posts at once
            )
        else:
            signals_result = await db.execute(
                select(Signal)
                .where(*query_conditions)
                .order_by(Signal.entry_time.desc())
                .limit(10)  # Limit to prevent too many posts at once
            )
        signals = signals_result.scalars().all()

        logger.info(f"Found {len(signals)} active signals for channel {channel.name}")

        # Log details about found signals
        for signal in signals:
            logger.info(f"  Signal {signal.id}: {signal.symbol} {signal.direction} at {signal.entry_price} (status: {signal.status.value}, entry: {signal.entry_time})")

        if not signals:
            logger.info(f"No new active trading signals to post for channel {channel.name}")
            return 0

        logger.info(f"Generating {len(signals)} signal posts for channel {channel.name}")

        # Process each signal
        for signal in signals:
            # Double-check that we haven't already created a signal post for this signal and channel
            # This is an additional safeguard against race conditions
            if is_sqlite_db():
                existing_post_result = db.execute(
                    select(Post)
                    .where(
                        Post.channel_id == channel.id,
                        Post.type == PostType.SIGNAL,
                        Post.signal_id == signal.id
                    )
                )
            else:
                existing_post_result = await db.execute(
                    select(Post)
                    .where(
                        Post.channel_id == channel.id,
                        Post.type == PostType.SIGNAL,
                        Post.signal_id == signal.id
                    )
                )

            existing_post = existing_post_result.scalars().first()
            if existing_post:
                logger.info(f"Signal post already exists for signal {signal.id} in channel {channel.name}, skipping")
                continue

            # Generate AI analysis
            safe_attrs = ensure_channel_attributes(channel)
            content = await qwen_client.analyze_trading_signal(
                signal,
                language=safe_attrs['language'],
                channel_brand=safe_attrs['brand_name']
            )

            # Skip this signal if AI generation failed
            if not content:
                logger.warning(f"Skipping signal {signal.id} for channel {channel.name} - AI generation failed")
                continue

            # Generate fresh signal chart if needed
            signal_chart_path = signal.chart_image
            if not signal_chart_path:
                try:
                    from src.visualization.chart_generator import generate_signal_chart
                    signal_chart_path = await generate_signal_chart(signal, chart_type="signal")
                    logger.info(f"Generated fresh signal chart: {signal_chart_path}")

                    # Update signal with new chart path
                    signal.chart_image = signal_chart_path
                    if is_sqlite_db():
                        db.commit()
                    else:
                        await db.commit()
                except Exception as chart_error:
                    logger.error(f"Error generating signal chart for signal {signal.id}: {chart_error}")

            # Create post - Send immediately for new signals (no delay)
            post = Post(
                channel_id=channel.id,
                type=PostType.SIGNAL,
                content=content,
                status=PostStatus.SCHEDULED,
                scheduled_time=now_utc,  # Send immediately (no delay) - use UTC
                signal_id=signal.id,
                image_path=signal_chart_path,  # Use fresh or existing chart
                created_at=now_utc  # Use UTC for database storage
            )

            db.add(post)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()
        logger.info(f"Created {len(signals)} signal posts for channel {channel.name}")
        return len(signals)

    except Exception as e:
        if is_sqlite_db():
            db.rollback()
        else:
            await db.rollback()
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"Error generating signal posts for channel {channel_name}: {e}", exc_info=True)
        return 0

async def generate_tp_hit_posts(db, channel, qwen_client):
    """
    Generate posts for individual TP level hits (TP1, TP2, TP3)

    Args:
        db: Database session
        channel (Channel): Channel to generate content for
        qwen_client (QwenClient): Qwen client for AI generation

    Returns:
        int: Number of TP hit posts created
    """
    try:
        # Defensive check for channel object
        if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
            logger.error(f"❌ Invalid channel object passed to generate_tp_hit_posts: {type(channel)} - {repr(channel)}")
            # Try to get channel name safely for logging
            channel_name = getattr(channel, 'name', str(channel))
            logger.error(f"❌ Expected Channel object with 'id' and 'name' attributes, got {type(channel).__name__}: {channel_name}")
            return 0
        from src.database.models import TakeProfitHit

        # Get recent TP hits not yet posted
        now = get_current_time()  # For time calculations
        now_utc = get_utc_time()  # For database storage
        yesterday = now - timedelta(days=1)

        # Get TP hits already posted for this channel
        if is_sqlite_db():
            posted_tp_result = db.execute(
                select(Post.signal_id, Post.tp_level)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.PERFORMANCE,
                    Post.tp_level.is_not(None)
                )
            )
        else:
            posted_tp_result = await db.execute(
                select(Post.signal_id, Post.tp_level)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.PERFORMANCE,
                    Post.tp_level.is_not(None)
                )
            )

        posted_tp_combinations = {(row[0], row[1]) for row in posted_tp_result.fetchall()}

        # Get recent TP hits that haven't been posted yet
        # We only look at TakeProfitHit records, not signal status, to avoid duplicates
        if is_sqlite_db():
            tp_hits_result = db.execute(
                select(TakeProfitHit)
                .where(
                    TakeProfitHit.hit_time >= yesterday
                )
                .order_by(TakeProfitHit.hit_time.desc())
                .limit(10)
            )
        else:
            tp_hits_result = await db.execute(
                select(TakeProfitHit)
                .where(
                    TakeProfitHit.hit_time >= yesterday
                )
                .order_by(TakeProfitHit.hit_time.desc())
                .limit(10)
            )

        tp_hits = tp_hits_result.scalars().all()

        posts_created = 0
        for tp_hit in tp_hits:
            # Skip if already posted
            if (tp_hit.signal_id, tp_hit.tp_level) in posted_tp_combinations:
                continue

            # Get the signal
            if is_sqlite_db():
                signal_result = db.execute(
                    select(Signal).where(Signal.id == tp_hit.signal_id)
                )
            else:
                signal_result = await db.execute(
                    select(Signal).where(Signal.id == tp_hit.signal_id)
                )

            signal = signal_result.scalars().first()
            if not signal:
                continue

            # Find original signal post to reply to
            reply_to_message_id = None
            logger.info(f"🔍 TP HIT: Searching for original signal post to reply to for TP{tp_hit.tp_level}...")

            try:
                # First, try to find a signal post with message_id (preferred)
                if is_sqlite_db():
                    original_post_result = db.execute(
                        select(Post).where(
                            Post.channel_id == channel.id,
                            Post.type == PostType.SIGNAL,
                            Post.signal_id == signal.id,
                            Post.message_id.isnot(None)
                        ).order_by(Post.created_at.desc()).limit(1)
                    )
                else:
                    original_post_result = await db.execute(
                        select(Post).where(
                            Post.channel_id == channel.id,
                            Post.type == PostType.SIGNAL,
                            Post.signal_id == signal.id,
                            Post.message_id.isnot(None)
                        ).order_by(Post.created_at.desc()).limit(1)
                    )

                original_post = original_post_result.scalars().first()

                # If no post with message_id found, try to find any signal post
                if not original_post:
                    logger.info(f"🔍 TP HIT: No signal post with message_id found, searching for any signal post...")
                    if is_sqlite_db():
                        original_post_result = db.execute(
                            select(Post).where(
                                Post.channel_id == channel.id,
                                Post.type == PostType.SIGNAL,
                                Post.signal_id == signal.id
                            ).order_by(Post.created_at.desc()).limit(1)
                        )
                    else:
                        original_post_result = await db.execute(
                            select(Post).where(
                                Post.channel_id == channel.id,
                                Post.type == PostType.SIGNAL,
                                Post.signal_id == signal.id
                            ).order_by(Post.created_at.desc()).limit(1)
                        )
                    original_post = original_post_result.scalars().first()

                logger.info(f"🔍 TP HIT: Database query result: {original_post}")

                if original_post:
                    logger.info(f"🔍 TP HIT: Found signal post: ID={original_post.id}, message_id={original_post.message_id}, status={original_post.status}")
                    if original_post.message_id:
                        # Use the first message ID if there are multiple (from chunked messages)
                        reply_to_message_id = original_post.message_id.split(',')[0]
                        logger.info(f"✅ TP HIT REPLY SETUP SUCCESS: TP{tp_hit.tp_level} hit will reply to message ID: {reply_to_message_id}")
                    else:
                        logger.warning(f"⚠️ TP HIT REPLY SETUP PARTIAL: Signal post found but no message_id - will post without reply")
                        reply_to_message_id = None
                else:
                    logger.error(f"❌ TP HIT REPLY SETUP FAILED: No signal post found for TP{tp_hit.tp_level} hit on signal {signal.id}")
                    reply_to_message_id = None
            except Exception as e:
                logger.error(f"❌ TP HIT REPLY SETUP ERROR: Exception finding original signal post: {e}", exc_info=True)
                reply_to_message_id = None

            # Generate PnL image as primary format
            logger.info(f"🖼️ Generating PnL image for TP{tp_hit.tp_level} hit (signal {signal.id})...")
            image_path = generate_pnl_image_from_signal(signal, tp_level=tp_hit.tp_level, tp_hit_record=tp_hit)

            # Generate AI content as fallback
            safe_attrs = ensure_channel_attributes(channel)
            content = await qwen_client.generate_signal_update(
                signal,
                language=safe_attrs['language'],
                channel_brand=safe_attrs['brand_name'],
                tp_level=tp_hit.tp_level
            )

            if not image_path and not content:
                logger.warning(f"Skipping TP{tp_hit.tp_level} hit post for signal {signal.id} - both image and text generation failed")
                continue

            # Use image as primary content, text as fallback
            if image_path:
                logger.info(f"✅ Using PnL image for TP{tp_hit.tp_level} hit: {image_path}")

                # Generate localized caption based on channel language
                channel_language = safe_attrs['language']
                post_content = get_performance_caption('tp_hit', channel_language, level=tp_hit.tp_level)

                # Add pips information for forex symbols only
                if hasattr(signal, 'symbol') and is_forex_pair(signal.symbol):
                    # Calculate pips for display
                    if hasattr(tp_hit, 'tp_price') and signal.entry_price:
                        pips = calculate_pips(signal.entry_price, tp_hit.tp_price, signal.symbol)
                        if pips > 0:
                            # TP hits are always profitable, so always show pips_profit
                            pips_caption = get_performance_caption('pips_profit', channel_language, pips=pips)
                            post_content += f"\n{pips_caption}"

                logger.info(f"📝 Generated localized TP hit caption ({channel_language}): {post_content}")

                # Store local image path in image_path field, not image_url
                image_url = None
                local_image_path = image_path
            else:
                logger.info(f"⚠️ Using text fallback for TP{tp_hit.tp_level} hit")
                post_content = content
                image_url = None
                local_image_path = None

            # Create TP hit post - Schedule like other content types
            post = Post(
                channel_id=channel.id,
                type=PostType.PERFORMANCE,
                content=post_content,
                image_url=image_url,
                image_path=local_image_path,  # Use image_path for local files
                status=PostStatus.SCHEDULED,
                scheduled_time=now_utc + timedelta(minutes=random.randint(60, 90)),  # 3-5 hours after greetings
                signal_id=signal.id,
                tp_level=tp_hit.tp_level,
                reply_to_message_id=reply_to_message_id,
                created_at=now_utc  # Use UTC for database storage
            )

            db.add(post)
            posts_created += 1
            logger.info(f"Created TP{tp_hit.tp_level} hit post for signal {signal.id} in channel {channel.name}")

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return posts_created

    except Exception as e:
        if is_sqlite_db():
            db.rollback()
        else:
            await db.rollback()
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"Error generating TP hit posts for channel {channel_name}: {e}", exc_info=True)
        return 0

async def generate_performance_posts(db, channel, qwen_client):
    """
    Generate performance posts for a channel

    Args:
        db: Database session
        channel (Channel): Channel to generate content for
        qwen_client (QwenClient): Qwen client for AI generation

    Returns:
        int: Number of performance posts created
    """
    try:
        # Defensive check for channel object
        if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
            logger.error(f"❌ Invalid channel object passed to generate_performance_posts: {type(channel)} - {channel}")
            return 0
        # Get signals that have been closed in the last 24 hours
        now = get_current_time()  # For time calculations
        now_utc = get_utc_time()  # For database storage
        yesterday = now - timedelta(days=1)

        # Get signals already posted as performance updates for this specific channel
        if is_sqlite_db():
            posted_performance_result = db.execute(
                select(Post.signal_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.PERFORMANCE,
                    Post.signal_id.is_not(None)
                )
            )
        else:
            posted_performance_result = await db.execute(
                select(Post.signal_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.PERFORMANCE,
                    Post.signal_id.is_not(None)
                )
            )

        # Extract signal IDs properly
        posted_performance_rows = posted_performance_result.all()
        posted_performance_ids = [row[0] for row in posted_performance_rows if row[0] is not None]
        logger.info(f"Found {len(posted_performance_ids)} existing performance posts for channel {channel.name}")
        logger.info(f"Already posted performance signal IDs: {posted_performance_ids}")

        # First, let's see ALL closed signals to understand what's available
        if is_sqlite_db():
            all_closed_result = db.execute(
                select(Signal)
                .where(
                    Signal.status.in_([SignalStatus.TP_HIT, SignalStatus.SL_HIT, SignalStatus.EXPIRED])
                )
                .order_by(Signal.exit_time.desc())
            )
        else:
            all_closed_result = await db.execute(
                select(Signal)
                .where(
                    Signal.status.in_([SignalStatus.TP_HIT, SignalStatus.SL_HIT, SignalStatus.EXPIRED])
                )
                .order_by(Signal.exit_time.desc())
            )
        all_closed_signals = all_closed_result.scalars().all()
        logger.info(f"Total closed signals in database: {len(all_closed_signals)}")
        for sig in all_closed_signals[:5]:  # Show first 5
            logger.info(f"  Closed signal {sig.id}: {sig.symbol} {sig.direction} (status: {sig.status.value}, exit: {sig.exit_time})")

        # Get recently closed signals not yet posted as performance updates
        # Only include FULLY closed signals, not partial TP hits
        closed_statuses = [
            SignalStatus.TP_HIT,  # Legacy single TP
            SignalStatus.ALL_TP_HIT,  # All TPs hit
            SignalStatus.SL_HIT,  # Stop loss hit
            SignalStatus.EXPIRED  # Signal expired
        ]
        # Note: TP1_HIT, TP2_HIT, TP3_HIT are handled by generate_tp_hit_posts() separately

        query_conditions = [
            Signal.exit_time >= yesterday,
            Signal.status.in_(closed_statuses),
            Signal.exit_time.isnot(None)  # Ensure exit_time is not null
        ]

        # Only add the exclusion condition if there are actually posted performance IDs
        if posted_performance_ids:
            query_conditions.append(~Signal.id.in_(posted_performance_ids))
            logger.info(f"Excluding {len(posted_performance_ids)} already posted performance updates: {posted_performance_ids}")
        else:
            logger.info("No performance posts found to exclude - all closed signals are candidates")

        if is_sqlite_db():
            signals_result = db.execute(
                select(Signal)
                .where(*query_conditions)
                .order_by(Signal.exit_time.desc())
                .limit(5)  # Limit performance posts to prevent spam
            )
        else:
            signals_result = await db.execute(
                select(Signal)
                .where(*query_conditions)
                .order_by(Signal.exit_time.desc())
                .limit(5)  # Limit performance posts to prevent spam
            )
        signals = signals_result.scalars().all()

        logger.info(f"Found {len(signals)} closed signals for performance posts for channel {channel.name} after filtering")

        logger.info(f"Found {len(signals)} closed signals for channel {channel.name}")
        for signal in signals:
            logger.info(f"  Signal {signal.id}: {signal.symbol} {signal.direction} - Status: {signal.status}, Exit time: {signal.exit_time}")

        if not signals:
            logger.info(f"No closed signals to post for channel {channel.name}")
            return 0

        logger.info(f"Generating {len(signals)} performance posts for channel {channel.name}")

        # Process each signal
        for signal in signals:
            # Refresh signal from database to ensure we have the latest status
            if is_sqlite_db():
                fresh_signal_result = db.execute(
                    select(Signal).where(Signal.id == signal.id)
                )
            else:
                fresh_signal_result = await db.execute(
                    select(Signal).where(Signal.id == signal.id)
                )

            fresh_signal = fresh_signal_result.scalars().first()
            if not fresh_signal:
                logger.warning(f"Signal {signal.id} not found in database, skipping")
                continue

            # Use the fresh signal data
            signal = fresh_signal
            logger.info(f"Processing performance update for signal {signal.id}: {signal.symbol} {signal.direction} - Status: {signal.status.value}, P&L: {signal.profit_loss}")

            # Double-check that we haven't already created a performance post for this signal and channel
            # This is an additional safeguard against race conditions
            if is_sqlite_db():
                existing_post_result = db.execute(
                    select(Post)
                    .where(
                        Post.channel_id == channel.id,
                        Post.type == PostType.PERFORMANCE,
                        Post.signal_id == signal.id
                    )
                )
            else:
                existing_post_result = await db.execute(
                    select(Post)
                    .where(
                        Post.channel_id == channel.id,
                        Post.type == PostType.PERFORMANCE,
                        Post.signal_id == signal.id
                    )
                )

            existing_post = existing_post_result.scalars().first()
            if existing_post:
                logger.info(f"Performance post already exists for signal {signal.id} in channel {channel.name}, skipping")
                continue

            # Generate PnL image as primary format
            logger.info(f"🖼️ Generating PnL image for final result (signal {signal.id})...")
            pnl_image_path = generate_pnl_image_from_signal(signal, tp_level=None)

            # Generate AI analysis as fallback
            safe_attrs = ensure_channel_attributes(channel)
            logger.info(f"Generating AI content for signal {signal.id} with status: {signal.status.value}")
            content = await qwen_client.generate_signal_update(
                signal,
                language=safe_attrs['language'],
                channel_brand=safe_attrs['brand_name']
            )

            # Skip this performance update if both image and AI generation failed
            if not pnl_image_path and not content:
                logger.warning(f"Skipping performance update for signal {signal.id} for channel {channel.name} - both image and text generation failed")
                continue

            # Use PnL image as primary, text as fallback
            if pnl_image_path:
                logger.info(f"✅ Using PnL image for final result: {pnl_image_path}")

                # Generate localized caption based on channel language
                channel_language = safe_attrs['language']
                post_content = get_performance_caption('trade_result', channel_language)

                # Add pips information for forex symbols only
                if hasattr(signal, 'symbol') and is_forex_pair(signal.symbol):
                    if signal.exit_price and signal.entry_price:
                        pips = calculate_pips(signal.entry_price, signal.exit_price, signal.symbol)
                        if pips > 0:
                            # Determine if profit or loss based on direction and prices
                            is_profit = ((signal.direction == "buy" and signal.exit_price > signal.entry_price) or
                                       (signal.direction == "sell" and signal.exit_price < signal.entry_price))

                            pips_type = 'pips_profit' if is_profit else 'pips_loss'
                            pips_caption = get_performance_caption(pips_type, channel_language, pips=pips)
                            post_content += f"\n{pips_caption}"

                            logger.info(f"📊 Pips caption: {signal.symbol} {signal.direction} | "
                                      f"Entry: {signal.entry_price} | Exit: {signal.exit_price} | "
                                      f"Pips: {pips} | Profit: {is_profit} | Caption: {pips_caption}")

                logger.info(f"📝 Generated localized trade result caption ({channel_language}): {post_content}")

                # Store local image path in image_path field, not image_url
                image_url = None
                local_image_path = pnl_image_path
            else:
                logger.info(f"⚠️ Using text fallback for final result")
                post_content = content
                local_image_path = None
                # Generate fresh performance chart as fallback to PnL image
                try:
                    from src.visualization.chart_generator import generate_signal_chart
                    chart_path = await generate_signal_chart(signal, chart_type="performance")
                    if chart_path:
                        local_image_path = chart_path
                        logger.info(f"Generated fallback performance chart: {chart_path}")
                    else:
                        # Use original chart as last resort (if it's a URL)
                        if signal.chart_image and signal.chart_image.startswith('http'):
                            image_url = signal.chart_image
                        logger.info(f"Using original chart image: {signal.chart_image}")
                except Exception as chart_error:
                    logger.error(f"Error generating performance chart for signal {signal.id}: {chart_error}")
                    # Use original chart as last resort (if it's a URL)
                    if signal.chart_image and signal.chart_image.startswith('http'):
                        image_url = signal.chart_image

            # Find the original signal post to reply to
            reply_to_message_id = None
            logger.info(f"🔍 AUTOMATED: Searching for original signal post to reply to...")
            logger.info(f"🔍 AUTOMATED: Looking for signal_id={signal.id}, channel_id={channel.id}, type=SIGNAL, status=PUBLISHED")

            try:
                # First, try to find a signal post with message_id (preferred)
                if is_sqlite_db():
                    signal_post_result = db.execute(
                        select(Post).where(
                            Post.channel_id == channel.id,
                            Post.signal_id == signal.id,
                            Post.type == PostType.SIGNAL,
                            Post.message_id.isnot(None)
                        ).order_by(Post.created_at.desc()).limit(1)
                    )
                else:
                    signal_post_result = await db.execute(
                        select(Post).where(
                            Post.channel_id == channel.id,
                            Post.signal_id == signal.id,
                            Post.type == PostType.SIGNAL,
                            Post.message_id.isnot(None)
                        ).order_by(Post.created_at.desc()).limit(1)
                    )

                signal_post = signal_post_result.scalars().first()

                # If no post with message_id found, try to find any signal post
                if not signal_post:
                    logger.info(f"🔍 AUTOMATED: No signal post with message_id found, searching for any signal post...")
                    if is_sqlite_db():
                        signal_post_result = db.execute(
                            select(Post).where(
                                Post.channel_id == channel.id,
                                Post.signal_id == signal.id,
                                Post.type == PostType.SIGNAL
                            ).order_by(Post.created_at.desc()).limit(1)
                        )
                    else:
                        signal_post_result = await db.execute(
                            select(Post).where(
                                Post.channel_id == channel.id,
                                Post.signal_id == signal.id,
                                Post.type == PostType.SIGNAL
                            ).order_by(Post.created_at.desc()).limit(1)
                        )
                    signal_post = signal_post_result.scalars().first()

                logger.info(f"🔍 AUTOMATED: Database query result: {signal_post}")

                if signal_post:
                    logger.info(f"🔍 AUTOMATED: Found signal post: ID={signal_post.id}, message_id={signal_post.message_id}, status={signal_post.status}")
                    if signal_post.message_id:
                        # Use the first message ID if there are multiple (from chunked messages)
                        reply_to_message_id = signal_post.message_id.split(',')[0]
                        logger.info(f"✅ AUTOMATED REPLY SETUP SUCCESS: Performance update will reply to message ID: {reply_to_message_id}")
                    else:
                        logger.warning(f"⚠️ AUTOMATED REPLY SETUP PARTIAL: Signal post found but no message_id - will post without reply")
                        reply_to_message_id = None
                else:
                    logger.error(f"❌ AUTOMATED REPLY SETUP FAILED: No signal post found for signal {signal.id} in channel {channel.id}")
                    reply_to_message_id = None
            except Exception as e:
                logger.error(f"❌ AUTOMATED REPLY SETUP ERROR: Exception finding original signal post: {e}", exc_info=True)
                reply_to_message_id = None

            # Create post - Schedule after greeting posts
            post = Post(
                channel_id=channel.id,
                type=PostType.PERFORMANCE,
                content=post_content,
                status=PostStatus.SCHEDULED,
                scheduled_time=now_utc + timedelta(minutes=random.randint(300, 420)),  # 5-7 hours after greetings
                signal_id=signal.id,
                image_url=image_url,  # Use for URL-based images
                image_path=local_image_path,  # Use for local image files
                reply_to_message_id=reply_to_message_id,
                created_at=now_utc  # Use UTC for database storage
            )

            db.add(post)
            logger.info(f"Created performance post for signal {signal.id} in channel {channel.name}")

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        # Verify posts were created
        if is_sqlite_db():
            verify_result = db.execute(
                select(Post.signal_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.PERFORMANCE,
                    Post.signal_id.in_([s.id for s in signals])
                )
            )
        else:
            verify_result = await db.execute(
                select(Post.signal_id)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.PERFORMANCE,
                    Post.signal_id.in_([s.id for s in signals])
                )
            )
        verified_posts = verify_result.scalars().all()
        logger.info(f"Verified {len(verified_posts)} performance posts were saved to database")

        logger.info(f"Created {len(signals)} performance posts for channel {channel.name}")
        return len(signals)

    except Exception as e:
        if is_sqlite_db():
            db.rollback()
        else:
            await db.rollback()
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"Error generating performance posts for channel {channel_name}: {e}", exc_info=True)
        return 0

async def generate_analysis_posts(db, channel, qwen_client, config):
    """
    Generate market analysis posts for a channel

    Args:
        db: Database session
        channel (Channel): Channel to generate content for
        qwen_client (QwenClient): Qwen client for AI generation
        config (Config): Global configuration

    Returns:
        int: Number of analysis posts created
    """
    try:
        # Defensive check for channel object
        if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
            logger.error(f"❌ Invalid channel object passed to generate_analysis_posts: {type(channel)} - {channel}")
            return 0
        # Check for recent analysis posts to prevent spam
        recent_analysis = await check_recent_posts(db, channel.id, PostType.ANALYSIS, hours_back=4)
        if recent_analysis > 0:
            logger.info(f"Found {recent_analysis} recent analysis posts for channel {channel.name}, skipping to prevent spam")
            return 0

        # Get symbols and timeframes from config
        symbols = config.symbols.split(",")
        timeframes = config.timeframes.split(",")

        # Randomly select a symbol and timeframe for analysis
        symbol = random.choice(symbols)
        timeframe = random.choice(timeframes)

        logger.info(f"Generating market analysis for {symbol} {timeframe} for channel {channel.name}")

        # Get candle data
        if is_sqlite_db():
            candles_result = db.execute(
                select(CandleData)
                .where(
                    CandleData.symbol == symbol,
                    CandleData.timeframe == timeframe
                )
                .order_by(CandleData.timestamp.desc())
                .limit(50)  # Get last 50 candles for analysis
            )
        else:
            candles_result = await db.execute(
                select(CandleData)
                .where(
                    CandleData.symbol == symbol,
                    CandleData.timeframe == timeframe
                )
                .order_by(CandleData.timestamp.desc())
                .limit(50)  # Get last 50 candles for analysis
            )
        candles = candles_result.scalars().all()

        if not candles:
            logger.warning(f"No candle data found for {symbol} {timeframe}")
            return 0

        # Generate AI analysis
        safe_attrs = ensure_channel_attributes(channel)
        content = await qwen_client.generate_market_analysis(
            symbol,
            timeframe,
            candles,
            language=safe_attrs['language'],
            channel_brand=safe_attrs['brand_name']
        )

        # Skip analysis if AI generation failed
        if not content:
            logger.warning(f"Skipping market analysis for {symbol} {timeframe} for channel {channel.name} - AI generation failed")
            return 0

        # Generate chart for the analysis
        chart_path = None
        try:
            from src.visualization.chart_generator import generate_analysis_chart
            chart_path = await generate_analysis_chart(symbol, timeframe, candles)
            if chart_path:
                logger.info(f"Generated analysis chart: {chart_path}")
            else:
                logger.warning(f"Failed to generate chart for {symbol} {timeframe}")
        except Exception as chart_error:
            logger.error(f"Error generating analysis chart: {chart_error}", exc_info=True)

        # Get timezone and times
        tz = get_timezone()
        now = datetime.now(tz)
        now_utc = get_utc_time()

        # Calculate scheduled time with proper timezone awareness - Schedule after greeting posts
        scheduled_time = now + timedelta(minutes=random.randint(420, 540))  # 7-9 hours after greetings
        scheduled_time_utc = scheduled_time.astimezone(pytz.UTC)  # Convert to UTC for database storage
        logger.info(f"Scheduling post for {scheduled_time.strftime('%Y-%m-%d %H:%M:%S %Z%z')} ({tz})")

        # Create post with chart image
        post = Post(
            channel_id=channel.id,
            type=PostType.ANALYSIS,
            content=content,
            status=PostStatus.SCHEDULED,
            scheduled_time=scheduled_time_utc,  # Store in UTC
            image_path=chart_path,  # Add chart image to the post
            created_at=now_utc  # Use UTC for database storage
        )

        db.add(post)
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        logger.info(f"Created market analysis post for {symbol} {timeframe} for channel {channel.name}")
        return 1

    except Exception as e:
        if is_sqlite_db():
            db.rollback()
        else:
            await db.rollback()
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"Error generating analysis post for channel {channel_name}: {e}", exc_info=True)
        return 0

async def download_image(url):
    """
    Download an image from a URL with simple approach and detailed logging.
    Falls back to a local default image if download fails.

    Args:
        url (str): URL of the image

    Returns:
        str: Path to the downloaded image, or path to default image if download failed
    """
    # Log the function call
    logger.info(f"Attempting to download image from: {url}")

    if not url:
        logger.info("No URL provided, returning default image")
        return get_default_image()

    # Create images directory if it doesn't exist
    os.makedirs("images", exist_ok=True)

    # Generate a stable filename based on the URL using MD5 hash
    url_hash = hashlib.md5(url.encode()).hexdigest()
    filename = f"images/{url_hash}.jpg"

    # If we already have this image cached, return it
    if os.path.exists(filename):
        logger.info(f"Using cached image: {filename}")
        return filename

    # Use a standard browser-like user agent
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
        "Referer": "https://www.google.com/"
    }

    try:
        # Log the download attempt
        logger.info(f"Downloading image from: {url}")

        # Download the image with a timeout
        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.get(url, headers=headers, follow_redirects=True)

            # Log the response status
            logger.info(f"Image download response: HTTP {response.status_code} - {url}")

            if response.status_code == 200:
                # Check if the content is actually an image
                content_type = response.headers.get('content-type', '')
                if not content_type.startswith('image/'):
                    logger.warning(f"Content is not an image (type: {content_type}): {url}")
                    return get_default_image()

                # Save the image
                try:
                    with open(filename, "wb") as f:
                        f.write(response.content)

                    logger.info(f"Successfully saved image to: {filename}")
                    return filename
                except Exception as save_error:
                    logger.error(f"Error saving image file: {save_error}")
                    return get_default_image()
            else:
                logger.warning(f"Failed to download image: HTTP {response.status_code} - {url}")
                return get_default_image()

    except Exception as e:
        logger.error(f"Error downloading image: {e} - {url}")
        return get_default_image()

def get_default_image():
    """
    Get the path to the default image to use when download fails.
    Uses a local static image file.

    Returns:
        str: Path to the default image
    """
    # Path to default image
    default_image = "static/images/default_news.jpg"

    # Check if the default image exists
    if os.path.exists(default_image):
        logger.info(f"Using default image: {default_image}")
        return default_image

    # If default image doesn't exist, try to create the directory
    os.makedirs("static/images", exist_ok=True)

    # Log that we're missing the default image
    logger.warning(f"Default image not found at {default_image}")

    # Try to create a simple default image using the helper script
    try:
        logger.info("Attempting to create default image")

        # Import the create_default_image function if available
        try:
            from create_default_image import create_default_image
            create_default_image(default_image)
            logger.info(f"Created default image at {default_image}")
            return default_image
        except ImportError:
            # If the helper script is not available, create a very simple image
            logger.warning("create_default_image module not found, using fallback method")

            # Create a simple colored image
            from PIL import Image, ImageDraw
            img = Image.new('RGB', (800, 450), color=(245, 245, 245))
            draw = ImageDraw.Draw(img)
            draw.rectangle([(0, 0), (799, 449)], outline=(200, 200, 200), width=2)
            draw.rectangle([(0, 0), (800, 60)], fill=(70, 130, 180))
            img.save(default_image)

            logger.info(f"Created simple default image at {default_image}")
            return default_image

    except Exception as e:
        logger.error(f"Failed to create default image: {e}")

        # If all else fails, return None
        return None


async def generate_greeting_posts(db, channel, qwen_client, force=False, global_sticker_path=None):
    """
    Generate daily greeting posts for a channel

    Args:
        db: Database session
        channel (Channel): Channel to generate greeting for
        qwen_client (QwenClient): Qwen client for AI generation
        force (bool): If True, ignore daily limit and generate greeting anyway
        global_sticker_path (str): Pre-generated sticker path to use for all channels
    """
    try:
        # Defensive check for channel object
        if not hasattr(channel, 'id') or not hasattr(channel, 'name'):
            logger.error(f"❌ Invalid channel object passed to generate_greeting_posts: {type(channel)} - {channel}")
            return 0

        from datetime import datetime, timezone, timedelta
        import random
        import pytz

        # Get current time in Tehran timezone
        tehran_tz = pytz.timezone('Asia/Tehran')
        now_tehran = datetime.now(tehran_tz)

        # Calculate today's boundaries in Tehran time
        today_start_tehran = now_tehran.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end_tehran = now_tehran.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Convert Tehran boundaries to UTC for database comparison
        # (Database stores created_at in UTC)
        today_start_utc = today_start_tehran.astimezone(pytz.UTC)
        today_end_utc = today_end_tehran.astimezone(pytz.UTC)

        logger.info(f"Checking for existing greeting posts for channel {channel.name}")
        logger.info(f"  Tehran today: {today_start_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')} to {today_end_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.info(f"  UTC range: {today_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')} to {today_end_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

        # Check for greeting posts today to prevent duplicates
        if is_sqlite_db():
            today_greetings_result = db.execute(
                select(Post)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.GREETING,
                    Post.created_at >= today_start_utc,
                    Post.created_at <= today_end_utc
                )
            )
        else:
            today_greetings_result = await db.execute(
                select(Post)
                .where(
                    Post.channel_id == channel.id,
                    Post.type == PostType.GREETING,
                    Post.created_at >= today_start_utc,
                    Post.created_at <= today_end_utc
                )
            )

        today_greetings = today_greetings_result.scalars().all()
        if today_greetings and not force:
            logger.info(f"Found {len(today_greetings)} greeting posts already created today for channel {channel.name}, skipping")
            return 0
        elif today_greetings and force:
            logger.info(f"Found {len(today_greetings)} greeting posts already created today for channel {channel.name}, but FORCE=True, continuing anyway")

        logger.info(f"Generating greeting message for channel {channel.name}")

        # Collect fresh events for greeting (this will also save them to database)
        logger.info(f"Collecting fresh events for greeting...")
        fresh_events = await collect_events_for_greeting()

        # Get today's economic events from database (using UTC boundaries for database comparison)
        logger.info(f"Fetching today's economic events for greeting...")
        if is_sqlite_db():
            events_result = db.execute(
                select(EconomicEvent)
                .where(
                    EconomicEvent.event_time >= today_start_utc,
                    EconomicEvent.event_time <= today_end_utc
                )
                .order_by(EconomicEvent.impact.desc(), EconomicEvent.event_time)
            )
        else:
            events_result = await db.execute(
                select(EconomicEvent)
                .where(
                    EconomicEvent.event_time >= today_start_utc,
                    EconomicEvent.event_time <= today_end_utc
                )
                .order_by(EconomicEvent.impact.desc(), EconomicEvent.event_time)
            )

        today_events = events_result.scalars().all()

        safe_attrs = ensure_channel_attributes(channel)
        logger.info(f"Generating greeting post with {len(today_events)} events for channel {channel.name}")
        logger.info(f"  Channel language: {safe_attrs['language']}")
        logger.info(f"  Channel brand: {safe_attrs['brand_name']}")

        # Generate greeting content
        greeting_content = await qwen_client.generate_daily_greeting(
            today_events,
            language=safe_attrs['language'],
            channel_brand=safe_attrs['brand_name']
        )

        # Skip greeting if AI generation failed
        if not greeting_content:
            logger.warning(f"Skipping greeting for channel {channel.name} - AI generation failed")
            return 0

        # Generate date sticker for this greeting (if enabled) and create separate posts
        utc_now = datetime.now(pytz.UTC)
        posts_created = 0

        # Use global sticker if provided, otherwise generate per-channel (backward compatibility)
        if getattr(channel, 'enable_date_stickers', True):  # Default to True for backward compatibility
            sticker_path = global_sticker_path

            if not sticker_path:
                # Fallback: generate per-channel sticker (for backward compatibility)
                logger.info(f"🎨 Generating date sticker for greeting message...")
                sticker_generator = DateStickerGenerator()
                sticker_path = sticker_generator.generate_sticker_for_channel(channel)

            if sticker_path:
                logger.info(f"✅ Using date sticker: {sticker_path}")

                # Create separate sticker post (sent FIRST) - image only, no text
                sticker_post = Post(
                    channel_id=channel.id,
                    type=PostType.GREETING,  # Use same type but different content
                    content="",  # Empty content - only send the sticker image (bot will skip text if empty)
                    status=PostStatus.SCHEDULED,
                    scheduled_time=utc_now + timedelta(seconds=random.randint(0, 30)),  # Send immediately
                    created_at=utc_now,
                    image_path=sticker_path  # Store sticker path for sending
                )

                db.add(sticker_post)
                posts_created += 1
                logger.info(f"✅ Created date sticker post (image only) for channel {channel.name}")
            else:
                logger.warning(f"⚠️ No date sticker available for channel {channel.name}")
        else:
            logger.info(f"📵 Date stickers disabled for channel {channel.name}, skipping sticker generation")

        # Create greeting text post (sent AFTER sticker)
        greeting_post = Post(
            channel_id=channel.id,
            type=PostType.GREETING,
            content=greeting_content,
            status=PostStatus.SCHEDULED,
            scheduled_time=utc_now + timedelta(minutes=random.randint(1, 2)),  # Send 1-2 minutes after sticker
            created_at=utc_now
            # No image_path - this is text-only
        )

        db.add(greeting_post)
        posts_created += 1

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        logger.info(f"✅ Successfully created {posts_created} greeting posts for channel {channel.name}")
        logger.info(f"  Sticker post scheduled for: {utc_now.strftime('%Y-%m-%d %H:%M:%S %Z')} (immediate)")
        logger.info(f"  Greeting post scheduled for: {(utc_now + timedelta(minutes=1)).strftime('%Y-%m-%d %H:%M:%S %Z')} (1-2 min later)")
        logger.info(f"  Content length: {len(greeting_content)} characters")
        return posts_created

    except Exception as e:
        # Safely get channel name for error logging
        channel_name = getattr(channel, 'name', str(channel))
        logger.error(f"❌ Error generating greeting posts for channel {channel_name}: {e}", exc_info=True)
        # Add rollback for safety
        if is_sqlite_db():
            db.rollback()
        else:
            await db.rollback()
        return 0
